<script lang="ts">
	import { onMount, createEventDispatcher } from 'svelte';
	import { noteManagementStore, filteredNotes, noteStats } from '$stores/noteManagement';
	import { <PERSON>ton, Card, Badge, Loading } from '$components/ui';
	import {
		Plus,
		Search,
		Filter,
		MoreVertical,
		Edit,
		Archive,
		Trash2,
		Copy,
		Eye,
		Calendar,
		Tag,
		User
	} from 'svelte-lucide';
	import type { Note, NoteStatus, NotePriority } from '$types';

	// Events
	const dispatch = createEventDispatcher<{
		create: void;
		edit: { note: Note };
		view: { note: Note };
	}>();

	// Props
	interface Props {
		authorId?: string;
	}

	const { authorId = 'user-1' }: Props = $props(); // TODO: Get from auth store

	// Local state
	let searchQuery = '';
	let statusFilter: NoteStatus | 'all' = 'all';
	let priorityFilter: NotePriority | 'all' = 'all';
	let showFilters = false;

	// Reactive statements
	const notes = $derived($filteredNotes);
	const stats = $derived($noteStats);
	const loading = $derived($noteManagementStore.loading);
	const error = $derived($noteManagementStore.error);

	// Update store filters when local state changes
	$effect(() => {
		noteManagementStore.setSearchQuery(searchQuery);
	});
	$effect(() => {
		noteManagementStore.setStatusFilter(statusFilter);
	});
	$effect(() => {
		noteManagementStore.setPriorityFilter(priorityFilter);
	});

	// Load notes on mount
	onMount(() => {
		loadNotes();
	});

	const loadNotes = async () => {
		await noteManagementStore.loadUserNotes(authorId);
	};

	const handleCreateNote = () => {
		dispatch('create');
	};

	const handleEditNote = (note: Note) => {
		noteManagementStore.setCurrentNote(note);
		dispatch('edit', { note });
	};

	const handleViewNote = (note: Note) => {
		noteManagementStore.setCurrentNote(note);
		dispatch('view', { note });
	};

	const handleArchiveNote = async (note: Note) => {
		await noteManagementStore.archiveNote(note.id);
	};

	const handleDeleteNote = async (note: Note) => {
		if (confirm(`確定要刪除筆記 "${note.title}" 嗎？此操作無法撤銷。`)) {
			await noteManagementStore.deleteNote(note.id);
		}
	};

	const handleDuplicateNote = async (note: Note) => {
		await noteManagementStore.duplicateNote(note.id, authorId);
	};

	const handlePublishNote = async (note: Note) => {
		await noteManagementStore.publishNote(note.id);
	};

	const getStatusColor = (status: NoteStatus) => {
		switch (status) {
			case 'draft':
				return 'secondary';
			case 'published':
				return 'success';
			case 'archived':
				return 'outline';
			default:
				return 'secondary';
		}
	};

	const getPriorityColor = (priority: NotePriority) => {
		switch (priority) {
			case 'urgent':
				return 'destructive';
			case 'high':
				return 'warning';
			case 'medium':
				return 'default';
			case 'low':
				return 'secondary';
			default:
				return 'secondary';
		}
	};

	const formatDate = (date: Date) => {
		return new Intl.DateTimeFormat('zh-TW', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		}).format(new Date(date));
	};
</script>

<div class="space-y-6">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold">我的筆記</h1>
			<p class="text-muted-foreground mt-1">管理和組織您的筆記</p>
		</div>

		<Button on:click={handleCreateNote}>
			<Plus class="mr-2 h-4 w-4" />
			新建筆記
		</Button>
	</div>

	<!-- Statistics -->
	<div class="grid grid-cols-2 md:grid-cols-4 gap-4">
		<Card class="p-4">
			<div class="text-2xl font-bold">{stats.total}</div>
			<div class="text-sm text-muted-foreground">總筆記</div>
		</Card>
		<Card class="p-4">
			<div class="text-2xl font-bold">{stats.draft}</div>
			<div class="text-sm text-muted-foreground">草稿</div>
		</Card>
		<Card class="p-4">
			<div class="text-2xl font-bold">{stats.published}</div>
			<div class="text-sm text-muted-foreground">已發布</div>
		</Card>
		<Card class="p-4">
			<div class="text-2xl font-bold">{stats.archived}</div>
			<div class="text-sm text-muted-foreground">已歸檔</div>
		</Card>
	</div>

	<!-- Search and Filters -->
	<Card class="p-4">
		<div class="space-y-4">
			<!-- Search -->
			<div class="relative">
				<Search
					class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
				/>
				<input
					type="text"
					placeholder="搜索筆記..."
					bind:value={searchQuery}
					class="input pl-10 pr-4"
				/>
			</div>

			<!-- Filter Toggle -->
			<div class="flex items-center justify-between">
				<Button variant="outline" size="sm" on:click={() => (showFilters = !showFilters)}>
					<Filter class="mr-2 h-4 w-4" />
					篩選
				</Button>

				{#if statusFilter !== 'all' || priorityFilter !== 'all'}
					<Button
						variant="ghost"
						size="sm"
						on:click={() => {
							statusFilter = 'all';
							priorityFilter = 'all';
						}}
					>
						清除篩選
					</Button>
				{/if}
			</div>

			<!-- Filters -->
			{#if showFilters}
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
					<div>
						<label class="block text-sm font-medium mb-2">狀態</label>
						<select
							bind:value={statusFilter}
							class="select"
						>
							<option value="all">全部狀態</option>
							<option value="draft">草稿</option>
							<option value="published">已發布</option>
							<option value="archived">已歸檔</option>
						</select>
					</div>

					<div>
						<label class="block text-sm font-medium mb-2">優先級</label>
						<select
							bind:value={priorityFilter}
							class="select"
						>
							<option value="all">全部優先級</option>
							<option value="urgent">緊急</option>
							<option value="high">高</option>
							<option value="medium">中</option>
							<option value="low">低</option>
						</select>
					</div>
				</div>
			{/if}
		</div>
	</Card>

	<!-- Loading State -->
	{#if loading}
		<div class="flex justify-center py-8">
			<Loading size="lg" text="載入筆記中..." />
		</div>
	{/if}

	<!-- Error State -->
	{#if error}
		<Card class="p-6 text-center">
			<div class="text-destructive mb-2">載入失敗</div>
			<div class="text-sm text-muted-foreground mb-4">{error}</div>
			<Button variant="outline" on:click={loadNotes}>重試</Button>
		</Card>
	{/if}

	<!-- Notes List -->
	{#if !loading && !error}
		{#if notes.length === 0}
			<Card class="p-8 text-center">
				<div class="text-muted-foreground mb-4">
					{searchQuery ? '沒有找到匹配的筆記' : '還沒有筆記'}
				</div>
				<Button on:click={handleCreateNote}>
					<Plus class="mr-2 h-4 w-4" />
					創建第一個筆記
				</Button>
			</Card>
		{:else}
			<div class="grid gap-4">
				{#each notes as note (note.id)}
					<Card class="p-6 hover:shadow-md transition-shadow">
						<div class="flex items-start justify-between">
							<div class="flex-1 min-w-0">
								<!-- Title and Status -->
								<div class="flex items-center gap-2 mb-2">
									<h3 class="text-lg font-semibold truncate">
										{note.title}
									</h3>
									<Badge variant={getStatusColor(note.status)} size="sm">
										{note.status === 'draft'
											? '草稿'
											: note.status === 'published'
												? '已發布'
												: '已歸檔'}
									</Badge>
									<Badge variant={getPriorityColor(note.priority)} size="sm">
										{note.priority === 'urgent'
											? '緊急'
											: note.priority === 'high'
												? '高'
												: note.priority === 'medium'
													? '中'
													: '低'}
									</Badge>
								</div>

								<!-- Excerpt -->
								{#if note.excerpt}
									<p class="text-muted-foreground text-sm mb-3 line-clamp-2">
										{note.excerpt}
									</p>
								{/if}

								<!-- Tags -->
								{#if note.tags.length > 0}
									<div class="flex flex-wrap gap-1 mb-3">
										{#each note.tags.slice(0, 3) as tag}
											<Badge variant="outline" size="sm">
												<Tag class="mr-1 h-3 w-3" />
												{tag.name}
											</Badge>
										{/each}
										{#if note.tags.length > 3}
											<Badge variant="outline" size="sm">
												+{note.tags.length - 3}
											</Badge>
										{/if}
									</div>
								{/if}

								<!-- Meta Info -->
								<div class="flex items-center gap-4 text-xs text-muted-foreground">
									<div class="flex items-center gap-1">
										<Calendar class="h-3 w-3" />
										{formatDate(note.updatedAt)}
									</div>
									<div class="flex items-center gap-1">
										<User class="h-3 w-3" />
										版本 {note.version}
									</div>
								</div>
							</div>

							<!-- Actions -->
							<div class="flex items-center gap-2 ml-4">
								<Button variant="ghost" size="sm" onclick={() => handleViewNote(note)}>
									<Eye class="h-4 w-4" />
								</Button>

								<Button variant="ghost" size="sm" onclick={() => handleEditNote(note)}>
									<Edit class="h-4 w-4" />
								</Button>

								<!-- More Actions Dropdown -->
								<div class="relative">
									<Button variant="ghost" size="sm">
										<MoreVertical class="h-4 w-4" />
									</Button>
									<!-- TODO: Implement dropdown menu -->
								</div>
							</div>
						</div>
					</Card>
				{/each}
			</div>
		{/if}
	{/if}
</div>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
