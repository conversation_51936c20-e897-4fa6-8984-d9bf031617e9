<script>
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import Icon from '$lib/components/ui/Icon.svelte';
  
  import Button from '$components/ui/Button.svelte';
  import Card from '$components/ui/Card.svelte';

  // 用戶資料數據
  let userProfile = $state({
    id: '1',
    name: '用戶',
    email: '<EMAIL>',
    avatar: '',
    bio: '這是一個示例用戶資料。',
    joinDate: '2024-01-01',
    lastLogin: '2024-12-01',
    preferences: {
      theme: 'system',
      language: 'zh-TW',
      notifications: true
    }
  });

  let isEditing = $state(false);
  let editForm = $state({
    name: '',
    email: '',
    bio: ''
  });

  onMount(() => {
    loadUserProfile();
  });

  const loadUserProfile = () => {
    // 在真實應用中，這裡會從 API 加載用戶資料
    editForm = {
      name: userProfile.name,
      email: userProfile.email,
      bio: userProfile.bio
    };
  };

  const handleEdit = () => {
    isEditing = true;
  };

  const handleSave = () => {
    userProfile.name = editForm.name;
    userProfile.email = editForm.email;
    userProfile.bio = editForm.bio;
    isEditing = false;
    
    // 在真實應用中，這裡會調用 API 保存資料
    console.log('保存用戶資料:', userProfile);
  };

  const handleCancel = () => {
    editForm = {
      name: userProfile.name,
      email: userProfile.email,
      bio: userProfile.bio
    };
    isEditing = false;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('zh-TW', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };
</script>

<svelte:head>
  <title>個人資料 - Life Note</title>
</svelte:head>

<div class="profile-page h-full flex flex-col">
  <!-- Header -->
  <header class="flex-shrink-0 border-b border-border bg-background/95 backdrop-blur">
    <div class="container mx-auto px-4 py-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold flex items-center">
            <Icon icon="user" size={24} class="mr-2" />
            個人資料
          </h1>
          <p class="text-muted-foreground mt-1">
            查看和編輯您的個人資料
          </p>
        </div>

        <div class="flex items-center space-x-2">
          {#if !isEditing}
            <Button onclick={handleEdit}>
              <Icon icon="edit" size={16} class="mr-2" />
              編輯資料
            </Button>
          {:else}
            <Button variant="outline" onclick={handleCancel}>
              取消
            </Button>
            <Button onclick={handleSave}>
              保存
            </Button>
          {/if}

          <Button variant="outline" onclick={() => goto('/settings')}>
            <Icon icon="settings" size={16} class="mr-2" />
            設置
          </Button>
        </div>
      </div>
    </div>
  </header>

  <!-- Content -->
  <main class="flex-1 overflow-auto">
    <div class="container mx-auto px-4 py-6">
      <div class="max-w-2xl mx-auto space-y-6">
        
        <!-- 基本資料 -->
        <Card class="p-6">
          <h2 class="text-lg font-semibold mb-4">基本資料</h2>
          
          {#if !isEditing}
            <div class="space-y-4">
              <div class="flex items-center space-x-4">
                <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                  <Icon icon="user" size={32} class="text-primary" />
                </div>
                <div>
                  <h3 class="text-xl font-semibold">{userProfile.name}</h3>
                  <p class="text-muted-foreground flex items-center">
                    <Icon icon="mail" size={16} class="mr-1" />
                    {userProfile.email}
                  </p>
                </div>
              </div>
              
              {#if userProfile.bio}
                <div>
                  <h4 class="font-medium mb-2">個人簡介</h4>
                  <p class="text-muted-foreground">{userProfile.bio}</p>
                </div>
              {/if}
            </div>
          {:else}
            <div class="space-y-4">
              <div>
                <label for="name" class="block text-sm font-medium mb-1">姓名</label>
                <input
                  id="name"
                  type="text"
                  bind:value={editForm.name}
                  class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                  placeholder="請輸入您的姓名"
                />
              </div>
              
              <div>
                <label for="email" class="block text-sm font-medium mb-1">電子郵件</label>
                <input
                  id="email"
                  type="email"
                  bind:value={editForm.email}
                  class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                  placeholder="請輸入您的電子郵件"
                />
              </div>
              
              <div>
                <label for="bio" class="block text-sm font-medium mb-1">個人簡介</label>
                <textarea
                  id="bio"
                  bind:value={editForm.bio}
                  rows="3"
                  class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                  placeholder="請輸入個人簡介"
                ></textarea>
              </div>
            </div>
          {/if}
        </Card>

        <!-- 帳戶資訊 -->
        <Card class="p-6">
          <h2 class="text-lg font-semibold mb-4">帳戶資訊</h2>
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium">加入日期</span>
              <span class="text-sm text-muted-foreground flex items-center">
                <Icon icon="calendar" size={16} class="mr-1" />
                {formatDate(userProfile.joinDate)}
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium">最後登入</span>
              <span class="text-sm text-muted-foreground">
                {formatDate(userProfile.lastLogin)}
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium">用戶 ID</span>
              <span class="text-sm text-muted-foreground font-mono">
                {userProfile.id}
              </span>
            </div>
          </div>
        </Card>

        <!-- 偏好設置 -->
        <Card class="p-6">
          <h2 class="text-lg font-semibold mb-4">偏好設置</h2>
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium">主題</span>
              <span class="text-sm text-muted-foreground capitalize">
                {userProfile.preferences.theme}
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium">語言</span>
              <span class="text-sm text-muted-foreground">
                {userProfile.preferences.language}
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium">通知</span>
              <span class="text-sm text-muted-foreground">
                {userProfile.preferences.notifications ? '已啟用' : '已停用'}
              </span>
            </div>
          </div>
          
          <div class="mt-4 pt-4 border-t">
            <Button variant="outline" size="sm" onclick={() => goto('/settings')}>
              管理偏好設置
            </Button>
          </div>
        </Card>
      </div>
    </div>
  </main>
</div>
