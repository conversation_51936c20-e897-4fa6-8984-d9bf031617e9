<script lang="ts">
	import { onMount, createEventDispatcher } from 'svelte';
	import { <PERSON><PERSON>, <PERSON>, Badge, Modal, Loading, Icon } from '$components/ui';
	import { versionService } from '$lib/services/versionService';
	import { toastStore } from '$stores/toast';
	import type { Note, NoteVersion } from '$types';

	// Props
	export let note: Note | null = null;
	export let open: boolean = false;

	// Events
	const dispatch = createEventDispatcher<{
		close: void;
		restore: { note: Note; version: NoteVersion };
		view: { version: NoteVersion };
	}>();

	// State
	let versions: NoteVersion[] = [];
	let loading = false;
	let error: string | null = null;
	let selectedVersion: NoteVersion | null = null;
	let showComparison = false;

	// Reactive effect
	$effect(() => {
		if (note && open) {
			loadVersionHistory();
		}
	});

	const loadVersionHistory = async () => {
		if (!note) return;

		loading = true;
		error = null;

		try {
			versions = await versionService.getVersionHistory(note.id);
		} catch (err) {
			error = err instanceof Error ? err.message : '載入版本歷史失敗';
			toastStore.error('載入失敗', error);
		} finally {
			loading = false;
		}
	};

	const handleClose = () => {
		selectedVersion = null;
		showComparison = false;
		dispatch('close');
	};

	const handleRestore = async (version: NoteVersion) => {
		if (!note) return;

		if (confirm(`確定要恢復到版本 ${version.version} 嗎？這將創建一個新的版本。`)) {
			try {
				const restoredNote = await versionService.restoreToVersion(
					note.id,
					version.id,
					'user-1' // TODO: Get from auth
				);

				toastStore.success('恢復成功', `已恢復到版本 ${version.version}`);
				dispatch('restore', { note: restoredNote, version });
				handleClose();
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : '恢復失敗';
				toastStore.error('恢復失敗', errorMessage);
			}
		}
	};

	const handleViewVersion = (version: NoteVersion) => {
		selectedVersion = version;
		dispatch('view', { version });
	};

	const formatDate = (date: Date) => {
		return new Intl.DateTimeFormat('zh-TW', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		}).format(new Date(date));
	};

	const getChangeTypeColor = (changeType: string) => {
		switch (changeType) {
			case 'major':
				return 'destructive';
			case 'title':
				return 'warning';
			case 'content':
				return 'default';
			case 'minor':
				return 'secondary';
			default:
				return 'outline';
		}
	};

	const getChangeTypeText = (changeType: string) => {
		switch (changeType) {
			case 'major':
				return '重大變更';
			case 'title':
				return '標題變更';
			case 'content':
				return '內容變更';
			case 'minor':
				return '微小變更';
			default:
				return '變更';
		}
	};
</script>

<Modal bind:open title="版本歷史" size="3xl" on:close={handleClose}>
	{#if note}
		<div class="space-y-4">
			<!-- Header Info -->
			<Card class="p-4">
				<div class="flex items-center justify-between">
					<div>
						<h3 class="font-semibold">{note.title}</h3>
						<p class="text-sm text-muted-foreground">
							當前版本: v{note.version}
						</p>
					</div>
					<Badge variant="default">
						{versions.length} 個版本
					</Badge>
				</div>
			</Card>

			<!-- Loading State -->
			{#if loading}
				<div class="flex justify-center py-8">
					<Loading size="lg" text="載入版本歷史..." />
				</div>
			{/if}

			<!-- Error State -->
			{#if error}
				<Card class="p-6 text-center">
					<div class="text-destructive mb-2">載入失敗</div>
					<div class="text-sm text-muted-foreground mb-4">{error}</div>
					<Button variant="outline" on:click={loadVersionHistory}>重試</Button>
				</Card>
			{/if}

			<!-- Version List -->
			{#if !loading && !error}
				{#if versions.length === 0}
					<Card class="p-8 text-center">
						<Icon icon="history" size={48} class="mx-auto mb-4 opacity-50" />
						<p class="text-muted-foreground">沒有版本歷史</p>
					</Card>
				{:else}
					<div class="space-y-3">
						{#each versions as version, index (version.id)}
							<Card class="p-4 hover:shadow-md transition-shadow">
								<div class="flex items-start justify-between">
									<div class="flex-1 min-w-0">
										<!-- Version Header -->
										<div class="flex items-center gap-2 mb-2">
											<Badge variant="outline" size="sm">
												v{version.version}
											</Badge>

											{#if version.metadata?.changeType}
												<Badge variant={getChangeTypeColor(version.metadata.changeType)} size="sm">
													{getChangeTypeText(version.metadata.changeType)}
												</Badge>
											{/if}

											{#if index === 0}
												<Badge variant="success" size="sm">最新</Badge>
											{/if}
										</div>

										<!-- Changes -->
										{#if version.changes}
											<p class="text-sm mb-2 text-muted-foreground">
												{version.changes}
											</p>
										{/if}

										<!-- Meta Info -->
										<div class="flex items-center gap-4 text-xs text-muted-foreground">
											<div class="flex items-center gap-1">
												<Icon icon="calendar" size={12} />
												{formatDate(version.createdAt)}
											</div>

											<div class="flex items-center gap-1">
												<Icon icon="user" size={12} />
												{version.authorId}
											</div>

											{#if version.metadata?.wordCount}
												<div class="flex items-center gap-1">
													<Icon icon="file-text" size={12} />
													{version.metadata.wordCount} 字符
												</div>
											{/if}
										</div>
									</div>

									<!-- Actions -->
									<div class="flex items-center gap-2 ml-4">
										<Button
											variant="ghost"
											size="sm"
											onclick={() => handleViewVersion(version)}
											title="查看此版本"
										>
											<Icon icon="eye" size={16} />
										</Button>

										{#if index !== 0}
											<Button
												variant="ghost"
												size="sm"
												onclick={() => handleRestore(version)}
												title="恢復到此版本"
											>
												<Icon icon="rotate-ccw" size={16} />
											</Button>
										{/if}
									</div>
								</div>

								<!-- Version Connection Line -->
								{#if index < versions.length - 1}
									<div class="flex justify-center mt-3">
										<div class="w-px h-4 bg-border"></div>
									</div>
								{/if}
							</Card>
						{/each}
					</div>
				{/if}
			{/if}
		</div>
	{/if}

	<!-- Footer -->
	<div slot="footer">
		<Button variant="outline" onclick={handleClose}>
			<Icon icon="x" size={16} class="mr-2" />
			關閉
		</Button>
	</div>
</Modal>
