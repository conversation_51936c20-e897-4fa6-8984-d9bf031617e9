<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { Button, Input, Badge, Modal, Icon } from '$components/ui';
	import MarkdownEditor from '$lib/components/editor/MarkdownEditor.svelte';
	import { noteManagementStore } from '$stores/noteManagement';
	import { toastStore } from '$stores/toast';
	import type { Note, NotePriority } from '$types';
	import type { CreateNoteData, UpdateNoteData } from '$lib/services/noteService';

	// Props
	export let note: Note | null = null; // If provided, we're editing
	export let open: boolean = false;
	export let authorId: string = 'user-1'; // TODO: Get from auth store

	// Events
	const dispatch = createEventDispatcher<{
		close: void;
		saved: { note: Note };
	}>();

	// Form state
	let title = '';
	let content = '';
	let priority: NotePriority = 'medium';
	let tags: string[] = [];
	let newTag = '';
	let saving = false;
	let showPreview = false;

	// Reactive statements
	$: isEditing = note !== null;
	$: modalTitle = isEditing ? '編輯筆記' : '創建筆記';
	$: canSave = title.trim().length > 0;

	// Initialize form when note changes
	$: if (note) {
		title = note.title;
		content = note.content;
		priority = note.priority;
		tags = note.tags.map(tag => tag.name);
	} else {
		resetForm();
	}

	const resetForm = () => {
		title = '';
		content = '';
		priority = 'medium';
		tags = [];
		newTag = '';
		saving = false;
	};

	const handleClose = () => {
		if (!saving) {
			resetForm();
			dispatch('close');
		}
	};

	const handleAddTag = () => {
		const trimmedTag = newTag.trim();
		if (trimmedTag && !tags.includes(trimmedTag)) {
			tags = [...tags, trimmedTag];
			newTag = '';
		}
	};

	const handleRemoveTag = (tagToRemove: string) => {
		tags = tags.filter(tag => tag !== tagToRemove);
	};

	const handleKeydown = (event: KeyboardEvent) => {
		if (event.key === 'Enter' && !event.shiftKey) {
			event.preventDefault();
			handleAddTag();
		}
	};

	const handleSave = async () => {
		if (!canSave || saving) return;

		saving = true;

		try {
			let savedNote: Note | null = null;

			if (isEditing && note) {
				// Update existing note
				const updateData: UpdateNoteData = {
					title: title.trim(),
					content: content.trim(),
					priority,
					tags
				};
				savedNote = await noteManagementStore.updateNote(note.id, updateData);
			} else {
				// Create new note
				const createData: CreateNoteData = {
					title: title.trim(),
					content: content.trim(),
					priority,
					tags
				};
				savedNote = await noteManagementStore.createNote(authorId, createData);
			}

			if (savedNote) {
				dispatch('saved', { note: savedNote });
				handleClose();
			}
		} catch (error) {
			console.error('Failed to save note:', error);
		} finally {
			saving = false;
		}
	};

	// Auto-save draft functionality (optional)
	let autoSaveTimeout: number;
	const autoSave = () => {
		if (autoSaveTimeout) {
			clearTimeout(autoSaveTimeout);
		}

		autoSaveTimeout = setTimeout(() => {
			if (isEditing && note && canSave) {
				// Auto-save as draft
				const updateData: UpdateNoteData = {
					title: title.trim(),
					content: content.trim(),
					priority,
					tags
				};
				noteManagementStore.updateNote(note.id, updateData);
			}
		}, 2000); // Auto-save after 2 seconds of inactivity
	};

	// Trigger auto-save when content changes
	$: if (title || content) {
		autoSave();
	}
</script>

<Modal bind:open {modalTitle} size="2xl" onclose={handleClose}>
	<form onsubmit|preventDefault={handleSave} class="space-y-6">
		<!-- Title -->
		<Input
			label="標題"
			placeholder="輸入筆記標題..."
			bind:value={title}
			required
			error={title.trim().length === 0 && title.length > 0 ? '標題不能為空' : ''}
		/>

		<!-- Priority -->
		<div class="space-y-2">
			<label class="text-sm font-medium">優先級</label>
			<div class="flex gap-2">
				{#each ['low', 'medium', 'high', 'urgent'] as p}
					<Button
						type="button"
						variant={priority === p ? 'default' : 'outline'}
						size="sm"
						onclick={() => (priority = p)}
					>
						{p === 'low' ? '低' : p === 'medium' ? '中' : p === 'high' ? '高' : '緊急'}
					</Button>
				{/each}
			</div>
		</div>

		<!-- Tags -->
		<div class="space-y-2">
			<label class="text-sm font-medium">標籤</label>

			<!-- Existing tags -->
			{#if tags.length > 0}
				<div class="flex flex-wrap gap-2 mb-2">
					{#each tags as tag}
						<Badge variant="secondary" removable onremove={() => handleRemoveTag(tag)}>
							<Icon icon="tag" class="mr-1 h-3 w-3" />
							{tag}
						</Badge>
					{/each}
				</div>
			{/if}

			<!-- Add new tag -->
			<div class="flex gap-2">
				<Input
					placeholder="添加標籤..."
					bind:value={newTag}
					onkeydown={handleKeydown}
					class="flex-1"
				/>
				<Button
					type="button"
					variant="outline"
					size="sm"
					onclick={handleAddTag}
					disabled={!newTag.trim() || tags.includes(newTag.trim())}
				>
					<Icon icon="plus" class="h-4 w-4" />
				</Button>
			</div>
		</div>

		<!-- Content Editor -->
		<div class="space-y-2">
			<div class="flex items-center justify-between">
				<label class="text-sm font-medium">內容</label>
				<div class="flex items-center gap-2">
					<Button
						type="button"
						variant={showPreview ? 'outline' : 'default'}
						size="sm"
						onclick={() => (showPreview = false)}
					>
						<Icon icon="edit" class="mr-1 h-3 w-3" />
						編輯
					</Button>
					<Button
						type="button"
						variant={showPreview ? 'default' : 'outline'}
						size="sm"
						onclick={() => (showPreview = true)}
					>
						<Icon icon="eye" class="mr-1 h-3 w-3" />
						預覽
					</Button>
				</div>
			</div>

			<div class="border border-input rounded-lg overflow-hidden">
				<MarkdownEditor
					bind:value={content}
					placeholder="開始寫作..."
					{showPreview}
					showToolbar={true}
					lineNumbers={true}
					lineWrapping={true}
					onchange={e => (content = e.detail.value)}
				/>
			</div>

			<!-- Character count -->
			<div class="text-xs text-muted-foreground text-right">
				{content.length} 字符
			</div>
		</div>
	</form>

	<!-- Footer Actions -->
	<div slot="footer">
		<Button variant="outline" onclick={handleClose} disabled={saving}>
			<Icon icon="x" class="mr-2 h-4 w-4" />
			取消
		</Button>

		<Button onclick={handleSave} disabled={!canSave || saving}>
			{#if saving}
				<div
					class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"
				></div>
			{:else}
				<Icon icon="save" class="mr-2 h-4 w-4" />
			{/if}
			{isEditing ? '更新' : '創建'}
		</Button>
	</div>
</Modal>
