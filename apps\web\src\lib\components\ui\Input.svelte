<script lang="ts">
	import { tv, type VariantProps } from 'tailwind-variants';
	import type { HTMLInputAttributes } from 'svelte/elements';

	const inputVariants = tv({
		base: 'flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
		variants: {
			variant: {
				default: '',
				error: 'border-destructive focus-visible:ring-destructive',
				success: 'border-green-500 focus-visible:ring-green-500'
			},
			size: {
				sm: 'h-8 px-2 text-xs',
				default: 'h-10',
				lg: 'h-12 px-4 text-base'
			}
		},
		defaultVariants: {
			variant: 'default',
			size: 'default'
		}
	});

	type Variant = VariantProps<typeof inputVariants>['variant'];
	type Size = VariantProps<typeof inputVariants>['size'];

	interface Props extends Omit<HTMLInputAttributes, 'size'> {
		variant?: Variant;
		size?: Size;
		class?: string;
		error?: string;
		label?: string;
		hint?: string;
		value?: string;
	}

	let {
		variant = 'default',
		size = 'default',
		error = '',
		label = '',
		hint = '',
		value = $bindable(''),
		class: className = '',
		...restProps
	}: Props = $props();

	// Auto-detect error variant
	const computedVariant = $derived(error ? 'error' : variant);
	const computedClass = $derived(inputVariants({ variant: computedVariant, size, class: className }));

	// Generate unique ID for accessibility
	const id = `input-${Math.random().toString(36).substr(2, 9)}`;
</script>

<div class="space-y-2">
	{#if label}
		<label
			for={id}
			class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
		>
			{label}
		</label>
	{/if}

	<input
		{id}
		bind:value
		class={computedClass}
		{...restProps}
	/>

	{#if error}
		<p class="text-sm text-destructive">{error}</p>
	{:else if hint}
		<p class="text-sm text-muted-foreground">{hint}</p>
	{/if}
</div>
