<script lang="ts">
	import { onMount } from 'svelte';
	import Icon from '$lib/components/ui/Icon.svelte';
	
	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import { simpleAgentManager } from '$lib/services/agent/SimpleAgentManager';
	import type { Note } from '$lib/types/note';

	// Props
	export let note: Note | null = null;
	export let onContentUpdate: ((content: string) => void) | null = null;
	export let compact = false;

	// 狀態
	let isExpanded = false;
	let isProcessing = false;
	let quickActions = [
		{
			id: 'improve',
			label: '改進文本',
			icon: 'sparkles',
			prompt: '請改進以下文本的表達和結構：'
		},
		{
			id: 'summarize',
			label: '生成摘要',
			icon: 'refresh-cw',
			prompt: '請為以下內容生成簡潔的摘要：'
		},
		{
			id: 'expand',
			label: '擴展內容',
			icon: 'wand-2',
			prompt: '請擴展以下內容，添加更多細節和例子：'
		},
		{
			id: 'questions',
			label: '生成問題',
			icon: 'message-square',
			prompt: '基於以下內容，生成一些思考問題：'
		}
	];

	let selectedText = '';
	let aiResult = '';
	let showResult = false;
	let copied = false;

	onMount(() => {
		// 監聽文本選擇
		document.addEventListener('selectionchange', handleSelectionChange);
		
		return () => {
			document.removeEventListener('selectionchange', handleSelectionChange);
		};
	});

	const handleSelectionChange = () => {
		const selection = window.getSelection();
		if (selection && selection.toString().trim()) {
			selectedText = selection.toString().trim();
		} else {
			selectedText = '';
		}
	};

	const executeQuickAction = async (action: any) => {
		if (!simpleAgentManager.isReady()) {
			alert('AI Agent 未就緒，請先設置 API Key');
			return;
		}

		const textToProcess = selectedText || (note?.content || '');
		if (!textToProcess.trim()) {
			alert('請選擇文本或確保筆記有內容');
			return;
		}

		isProcessing = true;
		aiResult = '';
		showResult = false;

		try {
			const request = {
				id: `quick-${action.id}-${Date.now()}`,
				type: 'content_generation' as const,
				priority: 'normal' as const,
				input: {
					prompt: `${action.prompt}\n\n${textToProcess}`,
					style: 'professional',
					maxLength: 1000
				},
				createdAt: new Date()
			};

			const result = await simpleAgentManager.executeTask(request);
			
			if (result.status === 'completed') {
				aiResult = result.output.content || result.output;
				showResult = true;
				isExpanded = true;
			} else {
				throw new Error(result.error || '處理失敗');
			}
		} catch (error) {
			console.error('Quick action failed:', error);
			alert(`執行失敗：${error instanceof Error ? error.message : '未知錯誤'}`);
		} finally {
			isProcessing = false;
		}
	};

	const copyResult = async () => {
		if (!aiResult) return;
		
		try {
			await navigator.clipboard.writeText(aiResult);
			copied = true;
			setTimeout(() => {
				copied = false;
			}, 2000);
		} catch (error) {
			console.error('Failed to copy:', error);
		}
	};

	const applyResult = () => {
		if (!aiResult || !onContentUpdate) return;
		
		if (selectedText) {
			// 替換選中的文本
			const currentContent = note?.content || '';
			const newContent = currentContent.replace(selectedText, aiResult);
			onContentUpdate(newContent);
		} else {
			// 追加到內容末尾
			const currentContent = note?.content || '';
			const newContent = currentContent + '\n\n' + aiResult;
			onContentUpdate(newContent);
		}
		
		showResult = false;
		aiResult = '';
		selectedText = '';
	};

	const clearResult = () => {
		aiResult = '';
		showResult = false;
		selectedText = '';
	};

	$: isAgentReady = simpleAgentManager.isReady();
	$: hasSelectedText = selectedText.length > 0;
</script>

<div class="agent-integration {compact ? 'compact' : ''}">
	{#if compact}
		<!-- 緊湊模式 - 浮動按鈕 -->
		<div class="fixed bottom-4 right-4 z-50">
			{#if hasSelectedText && isAgentReady}
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-2 mb-2">
					<div class="flex items-center space-x-1">
						{#each quickActions as action}
							<Button
								variant="ghost"
								size="sm"
								on:click={() => executeQuickAction(action)}
								disabled={isProcessing}
								title={action.label}
							>
								{#if isProcessing}
									<Icon icon="loader-2" size={16} class="animate-spin" />
								{:else}
									<Icon icon={action.icon} size={16} />
								{/if}
							</Button>
						{/each}
					</div>
				</div>
			{/if}
			
			<Button
				variant="default"
				size="sm"
				class="rounded-full shadow-lg"
				on:click={() => isExpanded = !isExpanded}
				disabled={!isAgentReady}
			>
				<Icon icon="bot" size={16} class="mr-2" />
				AI
				{#if isExpanded}
					<Icon icon="chevron-down" size={16} class="ml-1" />
				{:else}
					<Icon icon="chevron-up" size={16} class="ml-1" />
				{/if}
			</Button>
		</div>
	{:else}
		<!-- 完整模式 -->
		<Card class="p-4">
			<div class="flex items-center justify-between mb-4">
				<div class="flex items-center space-x-2">
					<Icon icon="bot" size={20} class="text-primary" />
					<h3 class="font-medium">AI 助手</h3>
					{#if hasSelectedText}
						<span class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded">
							已選擇 {selectedText.length} 字符
						</span>
					{/if}
				</div>
				
				<Button
					variant="ghost"
					size="sm"
					on:click={() => isExpanded = !isExpanded}
				>
					{#if isExpanded}
						<Icon icon="chevron-up" size={16} />
					{:else}
						<Icon icon="chevron-down" size={16} />
					{/if}
				</Button>
			</div>

			{#if !isAgentReady}
				<div class="text-center py-4">
					<Icon icon="bot" size={32} class="text-muted-foreground mx-auto mb-2" />
					<p class="text-sm text-muted-foreground">請先設置 Gemini API Key</p>
				</div>
			{:else}
				<!-- 快速操作按鈕 -->
				<div class="grid grid-cols-2 gap-2 mb-4">
					{#each quickActions as action}
						<Button
							variant="outline"
							size="sm"
							on:click={() => executeQuickAction(action)}
							disabled={isProcessing || (!hasSelectedText && !note?.content)}
							class="flex items-center justify-start space-x-2"
						>
							{#if isProcessing}
								<Icon icon="loader-2" size={16} class="animate-spin" />
							{:else}
								<Icon icon={action.icon} size={16} />
							{/if}
							<span class="text-sm">{action.label}</span>
						</Button>
					{/each}
				</div>
			{/if}
		</Card>
	{/if}

	<!-- 結果顯示 -->
	{#if showResult && aiResult}
		<div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50" on:click={clearResult}>
			<div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden" on:click|stopPropagation>
				<div class="p-4 border-b border-border">
					<div class="flex items-center justify-between">
						<h3 class="font-medium">AI 處理結果</h3>
						<div class="flex items-center space-x-2">
							<Button
								variant="ghost"
								size="sm"
								on:click={copyResult}
							>
								{#if copied}
									<Icon icon="check" size={16} class="text-green-600" />
								{:else}
									<Icon icon="copy" size={16} />
								{/if}
							</Button>
							<Button
								variant="ghost"
								size="sm"
								on:click={clearResult}
							>
								×
							</Button>
						</div>
					</div>
				</div>
				
				<div class="p-4 max-h-96 overflow-y-auto">
					<div class="prose prose-sm max-w-none">
						<pre class="whitespace-pre-wrap text-sm">{aiResult}</pre>
					</div>
				</div>
				
				<div class="p-4 border-t border-border flex justify-end space-x-2">
					<Button variant="outline" on:click={clearResult}>
						取消
					</Button>
					{#if onContentUpdate}
						<Button variant="default" on:click={applyResult}>
							{selectedText ? '替換選中文本' : '追加到筆記'}
						</Button>
					{/if}
				</div>
			</div>
		</div>
	{/if}

	<!-- 展開的面板（僅在完整模式下） -->
	{#if isExpanded && !compact && isAgentReady}
		<Card class="p-4 mt-4">
			<div class="space-y-4">
				<div>
					<h4 class="font-medium mb-2">使用提示</h4>
					<ul class="text-sm text-muted-foreground space-y-1">
						<li>• 選擇文本後點擊快速操作按鈕</li>
						<li>• 沒有選擇文本時會處理整個筆記內容</li>
						<li>• 可以將 AI 結果直接應用到筆記中</li>
						<li>• 支援複製結果到剪貼板</li>
					</ul>
				</div>
				
				{#if hasSelectedText}
					<div>
						<h4 class="font-medium mb-2">選中的文本</h4>
						<div class="p-3 bg-muted/30 rounded-lg text-sm">
							<pre class="whitespace-pre-wrap">{selectedText}</pre>
						</div>
					</div>
				{/if}
			</div>
		</Card>
	{/if}
</div>

<style>
	.agent-integration.compact {
		position: relative;
	}
	
	.prose pre {
		background: transparent;
		padding: 0;
		margin: 0;
		border: none;
	}
</style>
