<script lang="ts">
	import { onMount } from 'svelte';
	import {
		<PERSON>,
		Settings,
		Monitor,
		Cog
	} from 'svelte-lucide';
	import { <PERSON><PERSON>, Card } from '$components/ui';
	import { browser } from '$app/environment';
	import MCPServerConfig from './MCPServerConfig.svelte';
	import MCPServerStatus from './MCPServerStatus.svelte';
	import MCPTestConfig from './MCPTestConfig.svelte';
	import SimpleTest from './SimpleTest.svelte';

	// 狀態變數
	let mcpServers: Record<string, any> = {};
	let activeTab: 'status' | 'config' | 'test' = 'test';

	onMount(() => {
		if (browser) {
			loadMCPServers();
		}
	});

	// 載入 MCP 服務器配置
	function loadMCPServers() {
		if (!browser) return;
		
		try {
			const saved = localStorage.getItem('mcp-servers-config');
			if (saved) {
				mcpServers = JSON.parse(saved);
			}
		} catch (err) {
			console.error('Failed to load MCP servers config:', err);
		}
	}

	// 監聽配置變化
	function handleConfigChange() {
		loadMCPServers();
	}
</script>

<div class="mcp-dashboard space-y-6">
	<!-- 標題 -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold flex items-center gap-2">
				<Server class="h-8 w-8 text-primary" />
				MCP Server 管理
			</h1>
			<p class="text-muted-foreground mt-1">
				Model Context Protocol 服務器狀態和管理
			</p>
		</div>
	</div>

	<!-- 標籤頁導航 -->
	<div class="border-b border-border">
		<nav class="flex space-x-6">
			<button
				class="py-2 px-1 border-b-2 font-medium text-sm {activeTab === 'test' ? 'border-primary text-primary' : 'border-transparent text-muted-foreground hover:text-foreground'}"
				onclick={() => activeTab = 'test'}
			>
				<Settings class="h-4 w-4 inline mr-2" />
				功能測試
			</button>

			<button
				class="py-2 px-1 border-b-2 font-medium text-sm {activeTab === 'status' ? 'border-primary text-primary' : 'border-transparent text-muted-foreground hover:text-foreground'}"
				onclick={() => activeTab = 'status'}
			>
				<Monitor class="h-4 w-4 inline mr-2" />
				服務器狀態
			</button>

			<button
				class="py-2 px-1 border-b-2 font-medium text-sm {activeTab === 'config' ? 'border-primary text-primary' : 'border-transparent text-muted-foreground hover:text-foreground'}"
				onclick={() => activeTab = 'config'}
			>
				<Cog class="h-4 w-4 inline mr-2" />
				服務器配置
			</button>
		</nav>
	</div>

	<!-- 標籤頁內容 -->
	{#if activeTab === 'test'}
		<!-- 功能測試標籤頁 -->
		<div class="space-y-6">
			<SimpleTest />
			<MCPTestConfig />
		</div>

	{:else if activeTab === 'status'}
		<!-- 服務器狀態標籤頁 -->
		<div class="space-y-6">
			<!-- MCP 服務器狀態列表 -->
			{#if Object.keys(mcpServers).length > 0}
				<div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
					{#each Object.entries(mcpServers) as [key, server]}
						<MCPServerStatus serverKey={key} serverConfig={server} />
					{/each}
				</div>
			{:else}
				<Card class="p-8 text-center">
					<Server class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
					<h3 class="text-lg font-medium mb-2">沒有配置的 MCP 服務器</h3>
					<p class="text-muted-foreground mb-4">請先在配置標籤頁中添加 MCP 服務器</p>
					<Button onclick={() => activeTab = 'config'}>
						<Cog class="h-4 w-4 mr-2" />
						前往配置
					</Button>
				</Card>
			{/if}

			<!-- 使用說明 -->
			<Card class="p-6">
				<h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
					<Settings class="h-5 w-5" />
					MCP 配置說明
				</h3>
				
				<div class="space-y-4 text-sm">
					<div>
						<h4 class="font-medium mb-2">如何在 AI 客戶端中使用：</h4>
						<div class="bg-muted p-4 rounded-lg">
							<pre class="text-xs overflow-x-auto"><code>{JSON.stringify({
								"mcpServers": {
									"context7": {
										"command": "npx",
										"args": ["-y", "@upstash/context7-mcp"]
									},
									"serena": {
										"command": "uv",
										"args": ["run", "--directory", "C:\\Users\\<USER>\\Documents\\git\\serena", "serena-mcp-server"]
									},
									"life-note": {
										"command": "node",
										"args": ["apps/mcp-server/index.js"]
									}
								}
							}, null, 2)}</code></pre>
						</div>
					</div>
					
					<div>
						<h4 class="font-medium mb-2">支援的 MCP 服務器：</h4>
						<ul class="list-disc list-inside space-y-1 text-muted-foreground">
							<li><strong>Context7:</strong> 增強的上下文管理服務器</li>
							<li><strong>Serena:</strong> AI Agent 能力服務器</li>
							<li><strong>Life Note:</strong> 筆記管理服務器</li>
							<li><strong>自定義:</strong> 任何符合 MCP 協議的服務器</li>
						</ul>
					</div>
				</div>
			</Card>
		</div>

	{:else if activeTab === 'config'}
		<!-- 服務器配置標籤頁 -->
		<MCPServerConfig on:configChanged={handleConfigChange} />
	{/if}
</div>

<style>
	.mcp-dashboard {
		max-width: 1200px;
		margin: 0 auto;
	}
</style>
