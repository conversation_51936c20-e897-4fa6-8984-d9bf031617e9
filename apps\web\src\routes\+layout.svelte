<script lang="ts">
	import '../app.css';
	import { onMount } from 'svelte';
	import { page } from '$app/stores';

	import Header from '$components/layout/Header.svelte';
	import Sidebar from '$components/layout/Sidebar.svelte';
	import Footer from '$components/layout/Footer.svelte';
	import ToastContainer from '$components/ui/ToastContainer.svelte';
	import { themeStore } from '$stores/theme';
	import { appStore } from '$stores/app';
	import { dependencyMonitoringInitializer } from '$lib/services/dependencyMonitoringInitializer';

	// Svelte 5 runes mode: receive children prop
	interface Props {
		children: import('svelte').Snippet;
	}

	let { children }: Props = $props();

	// Initialize app and theme on mount
	onMount(async () => {
		try {
			await appStore.initialize();
			themeStore.initialize();

			// 延遲初始化依賴關係監控，確保筆記數據已載入
			setTimeout(async () => {
				try {
					await dependencyMonitoringInitializer.initialize();
				} catch (error) {
					console.error('Failed to initialize dependency monitoring:', error);
				}
			}, 2000);
		} catch (error) {
			console.error('Failed to initialize app:', error);
		}
	});

	// Reactive statements for layout
	const isFullscreen = $derived($page.route.id?.includes('(fullscreen)') ?? false);
	const showSidebar = $derived(!isFullscreen && $appStore.sidebarOpen);
</script>

<svelte:head>
	<title>Life Note - 智慧知識庫筆記系統</title>
</svelte:head>

<!-- Toast notifications -->
<ToastContainer />

<!-- Main app layout -->
<div class="min-h-screen bg-background">
	{#if !isFullscreen}
		<!-- Header -->
		<Header />

		<!-- Main content area -->
		<div class="flex">
			<!-- Sidebar -->
			{#if showSidebar}
				<aside class="w-64 border-r border-border bg-card">
					<Sidebar />
				</aside>
			{/if}

			<!-- Main content -->
			<main class="flex-1 overflow-hidden">
				<div class="h-full">
					{@render children()}
				</div>
			</main>
		</div>

		<!-- Footer -->
		<Footer />
	{:else}
		<!-- Fullscreen layout -->
		{@render children()}
	{/if}
</div>

<!-- Loading overlay -->
{#if $appStore.loading}
	<div
		class="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm"
	>
		<div class="flex flex-col items-center space-y-4">
			<div class="spinner h-8 w-8"></div>
			<p class="text-sm text-muted-foreground">載入中...</p>
		</div>
	</div>
{/if}

<!-- Error boundary -->
{#if $appStore.error}
	<div
		class="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm"
	>
		<div class="max-w-md rounded-lg border border-destructive bg-card p-6 shadow-lg">
			<h2 class="mb-2 text-lg font-semibold text-destructive">發生錯誤</h2>
			<p class="mb-4 text-sm text-muted-foreground">{$appStore.error}</p>
			<button
				class="rounded bg-primary px-4 py-2 text-sm text-primary-foreground hover:bg-primary/90"
				onclick={() => appStore.clearError()}
			>
				確定
			</button>
		</div>
	</div>
{/if}

<style>
	/* Custom scrollbar for main content */
	main {
		scrollbar-width: thin;
		scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
	}

	/* Responsive sidebar */
	@media (max-width: 768px) {
		aside {
			position: fixed;
			top: 0;
			left: 0;
			height: 100vh;
			z-index: 40;
			transform: translateX(-100%);
			transition: transform 0.3s ease-in-out;
		}

		aside.open {
			transform: translateX(0);
		}
	}

	/* Smooth transitions */
	main {
		transition: margin-left 0.3s ease-in-out;
	}

	/* Focus management */
	:global(.focus-trap) {
		outline: none;
	}

	/* Print styles */
	@media print {
		aside,
		header,
		footer {
			display: none !important;
		}

		main {
			margin: 0 !important;
			padding: 0 !important;
		}
	}
</style>
