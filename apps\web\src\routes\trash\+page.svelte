<script>
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { 
    Trash2, 
    Search, 
    RotateCcw, 
    X, 
    AlertTriangle,
    Calendar,
    Clock,
    CheckSquare,
    Square
  } from 'lucide-svelte';
  
  import Button from '$components/ui/Button.svelte';
  import Card from '$components/ui/Card.svelte';

  // 回收站數據
  let trashedNotes = $state([]);
  let searchQuery = $state('');
  let selectedNotes = $state(new Set());
  let showConfirmDialog = $state(false);
  let confirmAction = $state(null);
  let confirmMessage = $state('');

  // 過濾筆記
  const filteredNotes = $derived(trashedNotes.filter(note =>
    note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    note.content.toLowerCase().includes(searchQuery.toLowerCase())
  ));

  // 自動清理設置（天數）
  let autoCleanupDays = $state(30);

  onMount(() => {
    loadTrashedNotes();
  });

  const loadTrashedNotes = () => {
    // 模擬回收站數據
    trashedNotes = [
      {
        id: 'trash-1',
        title: '臨時筆記',
        content: '這是一個臨時的筆記內容...',
        tags: [
          { id: 'tag-1', name: '臨時', color: '#6b7280' }
        ],
        status: 'deleted',
        priority: 'low',
        createdAt: '2024-12-01T10:00:00Z',
        updatedAt: '2024-12-05T15:30:00Z',
        deletedAt: '2024-12-10T09:00:00Z'
      },
      {
        id: 'trash-2',
        title: '過期的會議記錄',
        content: '# 會議記錄\n\n這是一個過期的會議記錄...',
        tags: [
          { id: 'tag-2', name: '會議', color: '#ef4444' },
          { id: 'tag-3', name: '過期', color: '#6b7280' }
        ],
        status: 'deleted',
        priority: 'medium',
        createdAt: '2024-11-15T14:00:00Z',
        updatedAt: '2024-11-20T16:45:00Z',
        deletedAt: '2024-12-01T11:20:00Z'
      },
      {
        id: 'trash-3',
        title: '錯誤的筆記',
        content: '這個筆記是錯誤創建的...',
        tags: [],
        status: 'deleted',
        priority: 'low',
        createdAt: '2024-12-08T09:00:00Z',
        updatedAt: '2024-12-08T09:05:00Z',
        deletedAt: '2024-12-08T09:10:00Z'
      }
    ];
  };

  const restoreNote = (note) => {
    confirmAction = () => {
      trashedNotes = trashedNotes.filter(n => n.id !== note.id);
      showNotification(`筆記「${note.title}」已恢復`, 'success');
      showConfirmDialog = false;
    };
    confirmMessage = `確定要恢復筆記「${note.title}」嗎？`;
    showConfirmDialog = true;
  };

  const permanentlyDeleteNote = (note) => {
    confirmAction = () => {
      trashedNotes = trashedNotes.filter(n => n.id !== note.id);
      showNotification(`筆記「${note.title}」已永久刪除`, 'success');
      showConfirmDialog = false;
    };
    confirmMessage = `確定要永久刪除筆記「${note.title}」嗎？此操作無法撤銷。`;
    showConfirmDialog = true;
  };

  const restoreSelected = () => {
    if (selectedNotes.size === 0) return;
    
    confirmAction = () => {
      trashedNotes = trashedNotes.filter(note => !selectedNotes.has(note.id));
      showNotification(`已恢復 ${selectedNotes.size} 個筆記`, 'success');
      selectedNotes.clear();
      selectedNotes = selectedNotes; // 觸發響應式更新
      showConfirmDialog = false;
    };
    confirmMessage = `確定要恢復選中的 ${selectedNotes.size} 個筆記嗎？`;
    showConfirmDialog = true;
  };

  const deleteSelected = () => {
    if (selectedNotes.size === 0) return;
    
    confirmAction = () => {
      trashedNotes = trashedNotes.filter(note => !selectedNotes.has(note.id));
      showNotification(`已永久刪除 ${selectedNotes.size} 個筆記`, 'success');
      selectedNotes.clear();
      selectedNotes = selectedNotes; // 觸發響應式更新
      showConfirmDialog = false;
    };
    confirmMessage = `確定要永久刪除選中的 ${selectedNotes.size} 個筆記嗎？此操作無法撤銷。`;
    showConfirmDialog = true;
  };

  const emptyTrash = () => {
    if (trashedNotes.length === 0) return;
    
    confirmAction = () => {
      const count = trashedNotes.length;
      trashedNotes = [];
      selectedNotes.clear();
      selectedNotes = selectedNotes;
      showNotification(`已清空回收站，永久刪除 ${count} 個筆記`, 'success');
      showConfirmDialog = false;
    };
    confirmMessage = `確定要清空回收站嗎？這將永久刪除所有 ${trashedNotes.length} 個筆記，此操作無法撤銷。`;
    showConfirmDialog = true;
  };

  const toggleSelectNote = (noteId) => {
    if (selectedNotes.has(noteId)) {
      selectedNotes.delete(noteId);
    } else {
      selectedNotes.add(noteId);
    }
    selectedNotes = selectedNotes; // 觸發響應式更新
  };

  const toggleSelectAll = () => {
    if (selectedNotes.size === filteredNotes.length) {
      selectedNotes.clear();
    } else {
      selectedNotes = new Set(filteredNotes.map(note => note.id));
    }
    selectedNotes = selectedNotes; // 觸發響應式更新
  };

  const getDaysInTrash = (deletedAt) => {
    const deleted = new Date(deletedAt);
    const now = new Date();
    const diffTime = Math.abs(now - deleted);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getExpiryDate = (deletedAt) => {
    const deleted = new Date(deletedAt);
    const expiry = new Date(deleted.getTime() + (autoCleanupDays * 24 * 60 * 60 * 1000));
    return expiry;
  };

  const isExpiringSoon = (deletedAt) => {
    const daysInTrash = getDaysInTrash(deletedAt);
    return daysInTrash >= autoCleanupDays - 7; // 7天內過期
  };

  const showNotification = (message, type) => {
    console.log(`${type}: ${message}`);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('zh-TW', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getPreview = (content) => {
    return content.replace(/[#*`]/g, '').substring(0, 100) + '...';
  };
</script>

<svelte:head>
  <title>回收站 - Life Note</title>
</svelte:head>

<div class="trash-page h-full flex flex-col">
  <!-- Header -->
  <header class="flex-shrink-0 border-b border-border bg-background/95 backdrop-blur">
    <div class="container mx-auto px-4 py-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold flex items-center">
            <Trash2 class="h-6 w-6 mr-2" />
            回收站
          </h1>
          <p class="text-muted-foreground mt-1">
            管理已刪除的筆記，{autoCleanupDays} 天後自動永久刪除
          </p>
        </div>
        
        <div class="flex items-center space-x-2">
          {#if selectedNotes.size > 0}
            <Button variant="outline" size="sm" on:click={restoreSelected}>
              <RotateCcw class="h-4 w-4 mr-2" />
              恢復選中 ({selectedNotes.size})
            </Button>
            <Button variant="destructive" size="sm" on:click={deleteSelected}>
              <X class="h-4 w-4 mr-2" />
              永久刪除選中
            </Button>
          {/if}
          
          {#if trashedNotes.length > 0}
            <Button variant="destructive" onclick={emptyTrash}>
              <Trash2 class="h-4 w-4 mr-2" />
              清空回收站
            </Button>
          {/if}
        </div>
      </div>
      
      <!-- Search -->
      <div class="mt-6 flex items-center space-x-4">
        <div class="relative flex-1 max-w-md">
          <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="搜尋已刪除的筆記..."
            bind:value={searchQuery}
            class="w-full pl-10 pr-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
          />
        </div>
        
        {#if filteredNotes.length > 0}
          <Button
            variant="outline"
            size="sm"
            onclick={toggleSelectAll}
          >
            {#if selectedNotes.size === filteredNotes.length}
              <CheckSquare class="h-4 w-4 mr-2" />
              取消全選
            {:else}
              <Square class="h-4 w-4 mr-2" />
              全選
            {/if}
          </Button>
        {/if}
      </div>
    </div>
  </header>

  <!-- Content -->
  <main class="flex-1 overflow-auto">
    <div class="container mx-auto px-4 py-6">
      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Card class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-red-100 rounded-lg">
              <Trash2 class="h-5 w-5 text-red-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-muted-foreground">回收站筆記</p>
              <p class="text-2xl font-bold">{trashedNotes.length}</p>
            </div>
          </div>
        </Card>
        
        <Card class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-orange-100 rounded-lg">
              <AlertTriangle class="h-5 w-5 text-orange-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-muted-foreground">即將過期</p>
              <p class="text-2xl font-bold">
                {trashedNotes.filter(note => isExpiringSoon(note.deletedAt)).length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <Clock class="h-5 w-5 text-blue-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-muted-foreground">自動清理</p>
              <p class="text-2xl font-bold">{autoCleanupDays} 天</p>
            </div>
          </div>
        </Card>
      </div>

      <!-- Notes -->
      {#if filteredNotes.length > 0}
        <div class="space-y-4">
          {#each filteredNotes as note (note.id)}
            <Card class="p-4 hover:shadow-md transition-shadow {isExpiringSoon(note.deletedAt) ? 'border-orange-200 bg-orange-50/50' : ''}">
              <div class="flex items-start space-x-4">
                <!-- Checkbox -->
                <button
                  class="mt-1 p-1 rounded hover:bg-muted"
                  on:click={() => toggleSelectNote(note.id)}
                >
                  {#if selectedNotes.has(note.id)}
                    <CheckSquare class="h-4 w-4 text-primary" />
                  {:else}
                    <Square class="h-4 w-4 text-muted-foreground" />
                  {/if}
                </button>
                
                <!-- Content -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-start justify-between mb-2">
                    <h3 class="font-semibold text-lg line-clamp-1">{note.title}</h3>
                    
                    {#if isExpiringSoon(note.deletedAt)}
                      <div class="flex items-center text-orange-600 text-sm ml-2">
                        <AlertTriangle class="h-4 w-4 mr-1" />
                        即將過期
                      </div>
                    {/if}
                  </div>
                  
                  <p class="text-muted-foreground text-sm mb-3 line-clamp-2">
                    {getPreview(note.content)}
                  </p>
                  
                  <div class="flex flex-wrap gap-1 mb-3">
                    {#each note.tags as tag}
                      <span 
                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border"
                        style="background-color: {tag.color}20; border-color: {tag.color}; color: {tag.color}"
                      >
                        {tag.name}
                      </span>
                    {/each}
                  </div>
                  
                  <div class="flex items-center justify-between text-xs text-muted-foreground">
                    <div class="flex items-center space-x-4">
                      <span>刪除於 {formatDate(note.deletedAt)}</span>
                      <span>在回收站 {getDaysInTrash(note.deletedAt)} 天</span>
                      <span>將於 {formatDate(getExpiryDate(note.deletedAt))} 過期</span>
                    </div>
                    <span class="capitalize px-2 py-1 rounded-full bg-muted">
                      {note.priority}
                    </span>
                  </div>
                </div>
                
                <!-- Actions -->
                <div class="flex items-center space-x-1">
                  <Button variant="ghost" size="sm" onclick={() => restoreNote(note)}>
                    <RotateCcw class="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onclick={() => permanentlyDeleteNote(note)}>
                    <X class="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          {/each}
        </div>
      {:else}
        <div class="text-center py-12">
          <Trash2 class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 class="text-lg font-medium text-muted-foreground mb-2">
            {searchQuery ? '沒有找到匹配的筆記' : '回收站是空的'}
          </h3>
          <p class="text-muted-foreground">
            {searchQuery ? '嘗試使用不同的關鍵字搜尋' : '已刪除的筆記會出現在這裡'}
          </p>
        </div>
      {/if}
    </div>
  </main>
</div>

<!-- Confirmation Dialog -->
{#if showConfirmDialog}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <Card class="w-full max-w-md mx-4 p-6">
      <div class="flex items-center mb-4">
        <AlertTriangle class="h-6 w-6 text-orange-500 mr-3" />
        <h3 class="text-lg font-semibold">確認操作</h3>
      </div>
      
      <p class="text-muted-foreground mb-6">{confirmMessage}</p>
      
      <div class="flex justify-end space-x-2">
        <Button variant="outline" onclick={() => showConfirmDialog = false}>
          取消
        </Button>
        <Button variant="destructive" onclick={confirmAction}>
          確認
        </Button>
      </div>
    </Card>
  </div>
{/if}
