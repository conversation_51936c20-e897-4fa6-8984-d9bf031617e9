<script lang="ts">
	import { onMount } from 'svelte';
	import Icon from '$lib/components/ui/Icon.svelte';

	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import { simpleAgentManager } from '$lib/services/agent/SimpleAgentManager';
	import { allNotes } from '$stores/notes';
	import type { Note } from '$lib/types/note';

	// Props
	export let currentNote: Note | null = null;
	export let maxRecommendations = 5;

	// 狀態
	let recommendations: Array<{
		note: Note;
		relevanceScore: number;
		reason: string;
		type: 'similar' | 'related' | 'follow_up' | 'reference';
	}> = [];
	let isGenerating = false;
	let lastGenerated: Date | null = null;
	let error = '';

	// 智能內容建議
	let contentSuggestions: Array<{
		title: string;
		description: string;
		type: 'topic' | 'question' | 'expansion';
		priority: 'high' | 'medium' | 'low';
	}> = [];

	onMount(() => {
		if (currentNote && $allNotes && $allNotes.length > 1) {
			generateRecommendations();
		}
	});

	// 當前筆記變化時重新生成推薦
	$effect(() => {
		if (currentNote && $allNotes && $allNotes.length > 1) {
			generateRecommendations();
		}
	});

	const generateRecommendations = async () => {
		if (!currentNote || !simpleAgentManager.isReady() || !$allNotes || $allNotes.length <= 1) {
			return;
		}

		isGenerating = true;
		error = '';

		try {
			// 生成筆記推薦
			await generateNoteRecommendations();

			// 生成內容建議
			await generateContentSuggestions();

			lastGenerated = new Date();
		} catch (err) {
			console.error('Failed to generate recommendations:', err);
			error = err instanceof Error ? err.message : '生成推薦失敗';
		} finally {
			isGenerating = false;
		}
	};

	const generateNoteRecommendations = async () => {
		// 準備其他筆記的信息
		const otherNotes = $allNotes.filter(note => note.id !== currentNote!.id);
		const notesContext = otherNotes.map(note => ({
			id: note.id,
			title: note.title,
			content: note.content.substring(0, 200), // 只取前200字符
			tags: note.tags || [],
			createdAt: note.createdAt
		}));

		const request = {
			id: `recommend-${Date.now()}`,
			type: 'recommendation' as const,
			priority: 'normal' as const,
			input: {
				context: `當前筆記：
標題：${currentNote!.title}
內容：${currentNote!.content}
標籤：${(currentNote!.tags || []).join(', ')}

其他筆記：
${notesContext.map(note => `- ${note.title}: ${note.content}...`).join('\n')}`,
				type: 'note_recommendations',
				limit: maxRecommendations
			},
			createdAt: new Date()
		};

		const result = await simpleAgentManager.executeTask(request);

		if (result.status === 'completed') {
			// 解析 AI 回應並匹配實際筆記
			const aiRecommendations = result.output.recommendations || [];
			recommendations = aiRecommendations
				.map((rec: any) => {
					// 嘗試根據標題匹配筆記
					const matchedNote = otherNotes.find(
						note =>
							note.title.toLowerCase().includes(rec.title?.toLowerCase()) ||
							rec.title?.toLowerCase().includes(note.title.toLowerCase())
					);

					if (matchedNote) {
						return {
							note: matchedNote,
							relevanceScore: rec.relevance || 0.5,
							reason: rec.reason || '相關內容',
							type: rec.type || 'related'
						};
					}
					return null;
				})
				.filter(Boolean)
				.slice(0, maxRecommendations);

			// 如果 AI 推薦不夠，使用基於關鍵詞的簡單推薦
			if (recommendations.length < maxRecommendations) {
				const simpleRecommendations = generateSimpleRecommendations(otherNotes);
				recommendations = [...recommendations, ...simpleRecommendations].slice(
					0,
					maxRecommendations
				);
			}
		}
	};

	const generateContentSuggestions = async () => {
		const request = {
			id: `content-suggest-${Date.now()}`,
			type: 'content_generation' as const,
			priority: 'normal' as const,
			input: {
				prompt: `基於以下筆記，請提供內容擴展建議：

標題：${currentNote!.title}
內容：${currentNote!.content}

請以 JSON 格式提供建議，包含：
- title: 建議標題
- description: 建議描述
- type: 類型 (topic/question/expansion)
- priority: 優先級 (high/medium/low)

提供 3-5 個建議。`,
				style: 'professional',
				maxLength: 800
			},
			createdAt: new Date()
		};

		const result = await simpleAgentManager.executeTask(request);

		if (result.status === 'completed') {
			try {
				const content = result.output.content || result.output;
				// 嘗試解析 JSON
				const parsed = JSON.parse(content);
				if (Array.isArray(parsed)) {
					contentSuggestions = parsed;
				} else if (parsed.suggestions) {
					contentSuggestions = parsed.suggestions;
				}
			} catch {
				// 如果無法解析 JSON，生成默認建議
				contentSuggestions = [
					{
						title: '深入探討',
						description: '對當前主題進行更深入的研究和分析',
						type: 'expansion',
						priority: 'medium'
					},
					{
						title: '相關問題',
						description: '探索與此主題相關的問題和挑戰',
						type: 'question',
						priority: 'medium'
					}
				];
			}
		}
	};

	const generateSimpleRecommendations = (otherNotes: Note[]) => {
		if (!currentNote) return [];

		const currentTags = new Set((currentNote.tags || []).map(tag => tag.toLowerCase()));
		const currentWords = new Set(
			currentNote.content
				.toLowerCase()
				.split(/\s+/)
				.filter(word => word.length > 3)
		);

		return otherNotes
			.map(note => {
				let score = 0;
				let reasons = [];

				// 標籤相似性
				const noteTags = new Set((note.tags || []).map(tag => tag.toLowerCase()));
				const commonTags = [...currentTags].filter(tag => noteTags.has(tag));
				if (commonTags.length > 0) {
					score += commonTags.length * 0.3;
					reasons.push(`共同標籤: ${commonTags.join(', ')}`);
				}

				// 內容相似性（簡單詞匹配）
				const noteWords = new Set(
					note.content
						.toLowerCase()
						.split(/\s+/)
						.filter(word => word.length > 3)
				);
				const commonWords = [...currentWords].filter(word => noteWords.has(word));
				if (commonWords.length > 2) {
					score += Math.min(commonWords.length * 0.1, 0.5);
					reasons.push('內容相似');
				}

				// 標題相似性
				if (
					note.title.toLowerCase().includes(currentNote.title.toLowerCase()) ||
					currentNote.title.toLowerCase().includes(note.title.toLowerCase())
				) {
					score += 0.4;
					reasons.push('標題相關');
				}

				return {
					note,
					relevanceScore: score,
					reason: reasons.join(', ') || '可能相關',
					type: 'similar' as const
				};
			})
			.filter(rec => rec.relevanceScore > 0.1)
			.sort((a, b) => b.relevanceScore - a.relevanceScore);
	};

	const getTypeIcon = (type: string) => {
		switch (type) {
			case 'similar':
				return 'file-text';
			case 'related':
				return 'link';
			case 'follow_up':
				return 'trending-up';
			case 'reference':
				return 'external-link';
			default:
				return 'file-text';
		}
	};

	const getTypeLabel = (type: string) => {
		switch (type) {
			case 'similar':
				return '相似內容';
			case 'related':
				return '相關主題';
			case 'follow_up':
				return '後續閱讀';
			case 'reference':
				return '參考資料';
			default:
				return '推薦';
		}
	};

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case 'high':
				return 'text-red-600 bg-red-50';
			case 'medium':
				return 'text-yellow-600 bg-yellow-50';
			case 'low':
				return 'text-blue-600 bg-blue-50';
			default:
				return 'text-gray-600 bg-gray-50';
		}
	};

	const getSuggestionTypeIcon = (type: string) => {
		switch (type) {
			case 'topic':
				return 'lightbulb';
			case 'question':
				return 'file-text';
			case 'expansion':
				return 'trending-up';
			default:
				return 'lightbulb';
		}
	};
</script>

<div class="space-y-6">
	<!-- 筆記推薦 -->
	<Card class="p-6">
		<div class="flex items-center justify-between mb-4">
			<div class="flex items-center space-x-2">
				<Icon icon="lightbulb" size={20} class="text-yellow-500" />
				<h3 class="font-semibold">智能推薦</h3>
			</div>

			<div class="flex items-center space-x-2">
				{#if lastGenerated}
					<span class="text-xs text-muted-foreground">
						{lastGenerated.toLocaleTimeString()}
					</span>
				{/if}
				<Button
					variant="outline"
					size="sm"
					onclick={generateRecommendations}
					disabled={isGenerating || !simpleAgentManager.isReady()}
				>
					<Icon icon="refresh-cw" size={16} class="mr-1 {isGenerating ? 'animate-spin' : ''}" />
					刷新
				</Button>
			</div>
		</div>

		{#if error}
			<div class="p-3 bg-red-50 border border-red-200 rounded-lg mb-4">
				<p class="text-sm text-red-800">{error}</p>
			</div>
		{/if}

		{#if isGenerating}
			<div class="flex items-center justify-center py-8">
				<Icon icon="loader-2" size={24} class="animate-spin text-primary mr-2" />
				<span>正在生成智能推薦...</span>
			</div>
		{:else if !currentNote}
			<div class="text-center py-8">
				<Icon icon="file-text" size={48} class="text-muted-foreground mx-auto mb-3" />
				<p class="text-muted-foreground">請選擇一個筆記來獲取推薦</p>
			</div>
		{:else if recommendations.length === 0}
			<div class="text-center py-8">
				<Icon icon="lightbulb" size={48} class="text-muted-foreground mx-auto mb-3" />
				<p class="text-muted-foreground">暫無相關推薦</p>
			</div>
		{:else}
			<div class="space-y-3">
				{#each recommendations as rec}
					<div class="border rounded-lg p-4 hover:bg-muted/30 transition-colors">
						<div class="flex items-start justify-between">
							<div class="flex-1">
								<div class="flex items-center space-x-2 mb-2">
									<svelte:component this={getTypeIcon(rec.type)} class="h-4 w-4 text-primary" />
									<h4 class="font-medium">{rec.note.title}</h4>
									<span class="px-2 py-1 text-xs rounded-full bg-primary/10 text-primary">
										{getTypeLabel(rec.type)}
									</span>
								</div>
								<p class="text-sm text-muted-foreground mb-2">
									{rec.note.content.substring(0, 100)}...
								</p>
								<div class="flex items-center space-x-4 text-xs text-muted-foreground">
									<span>相關度: {(rec.relevanceScore * 100).toFixed(0)}%</span>
									<span>{rec.reason}</span>
									{#if rec.note.tags && rec.note.tags.length > 0}
										<div class="flex items-center space-x-1">
											<Icon icon="tag" size={12} />
											<span>{rec.note.tags.slice(0, 2).join(', ')}</span>
										</div>
									{/if}
								</div>
							</div>
							<Button variant="ghost" size="sm">
								<Icon icon="external-link" size={16} />
							</Button>
						</div>
					</div>
				{/each}
			</div>
		{/if}
	</Card>

	<!-- 內容建議 -->
	{#if contentSuggestions.length > 0}
		<Card class="p-6">
			<div class="flex items-center space-x-2 mb-4">
				<Icon icon="trending-up" size={20} class="text-green-500" />
				<h3 class="font-semibold">內容擴展建議</h3>
			</div>

			<div class="space-y-3">
				{#each contentSuggestions as suggestion}
					<div class="border rounded-lg p-4">
						<div class="flex items-start space-x-3">
							<svelte:component
								this={getSuggestionTypeIcon(suggestion.type)}
								class="h-5 w-5 text-primary mt-0.5"
							/>
							<div class="flex-1">
								<div class="flex items-center space-x-2 mb-1">
									<h4 class="font-medium">{suggestion.title}</h4>
									<span
										class="px-2 py-1 text-xs rounded-full {getPriorityColor(suggestion.priority)}"
									>
										{suggestion.priority}
									</span>
								</div>
								<p class="text-sm text-muted-foreground">{suggestion.description}</p>
							</div>
						</div>
					</div>
				{/each}
			</div>
		</Card>
	{/if}
</div>
