<script lang="ts">
	import { onMount } from 'svelte';
	import {
		Play,
		Square,
		Loader2,
		CheckCircle,
		AlertCircle,
		Bot,
		MessageSquare,
		FileText,
		Languages,
		Search,
		Lightbulb
	} from 'svelte-lucide';

	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import { simpleAgentManager } from '$lib/services/agent/SimpleAgentManager';
	import type { AgentTaskType, AgentTaskRequest, AgentTaskResult } from '$lib/types/agent';

	// Props
	export let taskType: AgentTaskType = 'question_answering';
	export let title = 'AI 任務執行器';
	export let description = '測試和使用 AI Agent 功能';

	// 狀態
	let isRunning = false;
	let taskInput = '';
	let taskOptions: any = {};
	let currentTask: AgentTaskRequest | null = null;
	let taskResult: AgentTaskResult | null = null;
	let taskHistory: AgentTaskResult[] = [];

	// 任務類型配置
	const taskConfigs = {
		question_answering: {
			icon: MessageSquare,
			label: '問答',
			placeholder: '請輸入您的問題...',
			inputLabel: '問題',
			options: [
				{
					key: 'language',
					label: '語言',
					type: 'select',
					values: ['zh-TW', 'en', 'ja'],
					default: 'zh-TW'
				}
			]
		},
		content_generation: {
			icon: FileText,
			label: '內容生成',
			placeholder: '請輸入內容生成提示...',
			inputLabel: '提示',
			options: [
				{ key: 'maxLength', label: '最大長度', type: 'number', default: 500 },
				{
					key: 'style',
					label: '風格',
					type: 'select',
					values: ['professional', 'casual', 'academic'],
					default: 'professional'
				}
			]
		},
		summarization: {
			icon: FileText,
			label: '文本摘要',
			placeholder: '請輸入要摘要的文本...',
			inputLabel: '原文',
			options: [
				{ key: 'maxLength', label: '摘要長度', type: 'number', default: 200 },
				{
					key: 'language',
					label: '語言',
					type: 'select',
					values: ['zh-TW', 'en'],
					default: 'zh-TW'
				}
			]
		},
		translation: {
			icon: Languages,
			label: '翻譯',
			placeholder: '請輸入要翻譯的文本...',
			inputLabel: '原文',
			options: [
				{
					key: 'targetLanguage',
					label: '目標語言',
					type: 'select',
					values: ['zh-TW', 'en', 'ja', 'ko'],
					default: 'en'
				},
				{
					key: 'sourceLanguage',
					label: '源語言',
					type: 'select',
					values: ['auto', 'zh-TW', 'en', 'ja', 'ko'],
					default: 'auto'
				}
			]
		},
		classification: {
			icon: Search,
			label: '文本分類',
			placeholder: '請輸入要分類的文本...',
			inputLabel: '文本',
			options: [
				{
					key: 'categories',
					label: '分類選項',
					type: 'text',
					placeholder: '用逗號分隔，如：技術,生活,工作'
				}
			]
		},
		note_analysis: {
			icon: Lightbulb,
			label: '筆記分析',
			placeholder: '請輸入筆記內容...',
			inputLabel: '筆記內容',
			options: [{ key: 'title', label: '筆記標題', type: 'text', placeholder: '筆記標題' }]
		}
	};

	const currentConfig = $derived(taskConfigs[taskType] || taskConfigs.question_answering);

	onMount(() => {
		// 初始化選項
		initializeOptions();

		// 載入歷史記錄
		loadTaskHistory();
	});

	const initializeOptions = () => {
		taskOptions = {};
		currentConfig.options?.forEach(option => {
			if (option.default !== undefined) {
				taskOptions[option.key] = option.default;
			}
		});
	};

	const loadTaskHistory = () => {
		const saved = localStorage.getItem(`agent_task_history_${taskType}`);
		if (saved) {
			try {
				taskHistory = JSON.parse(saved);
			} catch (error) {
				console.error('Failed to load task history:', error);
			}
		}
	};

	const saveTaskHistory = () => {
		try {
			localStorage.setItem(
				`agent_task_history_${taskType}`,
				JSON.stringify(taskHistory.slice(-10))
			); // 只保存最近10個
		} catch (error) {
			console.error('Failed to save task history:', error);
		}
	};

	const executeTask = async () => {
		if (!taskInput.trim()) {
			alert('請輸入任務內容');
			return;
		}

		if (!simpleAgentManager.isReady()) {
			alert('Agent 未就緒，請先設置 API Key');
			return;
		}

		isRunning = true;
		taskResult = null;

		try {
			// 準備任務輸入
			const input = prepareTaskInput();

			// 創建任務請求
			const request: AgentTaskRequest = {
				id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
				type: taskType,
				priority: 'normal',
				input,
				options: taskOptions,
				createdAt: new Date(),
				timeout: 60000
			};

			currentTask = request;

			// 執行任務
			const result = await simpleAgentManager.executeTask(request);

			taskResult = result;

			// 添加到歷史記錄
			taskHistory = [result, ...taskHistory.slice(0, 9)];
			saveTaskHistory();
		} catch (error) {
			console.error('Task execution failed:', error);
			taskResult = {
				id: `error-${Date.now()}`,
				taskId: currentTask?.id || 'unknown',
				status: 'failed',
				error: error instanceof Error ? error.message : String(error),
				executionTime: 0,
				completedAt: new Date(),
				agentId: 'primary-gemini-agent'
			};
		} finally {
			isRunning = false;
			currentTask = null;
		}
	};

	const prepareTaskInput = () => {
		const input: any = {};

		switch (taskType) {
			case 'question_answering':
				input.question = taskInput;
				input.language = taskOptions.language;
				break;
			case 'content_generation':
				input.prompt = taskInput;
				input.maxLength = taskOptions.maxLength;
				input.style = taskOptions.style;
				break;
			case 'summarization':
				input.text = taskInput;
				input.maxLength = taskOptions.maxLength;
				input.language = taskOptions.language;
				break;
			case 'translation':
				input.text = taskInput;
				input.targetLanguage = taskOptions.targetLanguage;
				input.sourceLanguage = taskOptions.sourceLanguage;
				break;
			case 'classification':
				input.text = taskInput;
				if (taskOptions.categories) {
					input.categories = taskOptions.categories.split(',').map((c: string) => c.trim());
				}
				break;
			case 'note_analysis':
				input.content = taskInput;
				input.title = taskOptions.title || '未命名筆記';
				break;
			default:
				input.text = taskInput;
		}

		return input;
	};

	const clearResult = () => {
		taskResult = null;
		taskInput = '';
	};

	const formatResult = (result: AgentTaskResult) => {
		if (result.status === 'failed') {
			return result.error || '執行失敗';
		}

		if (!result.output) {
			return '無輸出結果';
		}

		// 根據任務類型格式化結果
		switch (taskType) {
			case 'question_answering':
				return result.output.answer || result.output;
			case 'content_generation':
				return result.output.content || result.output;
			case 'summarization':
				return result.output.summary || result.output;
			case 'translation':
				return result.output.translatedText || result.output;
			case 'classification':
				return `分類：${result.output.category || '未知'}\n信心度：${((result.output.confidence || 0) * 100).toFixed(1)}%`;
			case 'note_analysis':
				if (typeof result.output === 'object') {
					return `情感：${result.output.sentiment || '未知'}\n主題：${(result.output.topics || []).join(', ')}\n複雜度：${result.output.complexity || '未知'}`;
				}
				return result.output;
			default:
				return JSON.stringify(result.output, null, 2);
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'completed':
				return 'text-green-600';
			case 'failed':
				return 'text-red-600';
			case 'running':
				return 'text-blue-600';
			default:
				return 'text-gray-600';
		}
	};

	const getStatusIcon = (status: string) => {
		switch (status) {
			case 'completed':
				return CheckCircle;
			case 'failed':
				return AlertCircle;
			case 'running':
				return Loader2;
			default:
				return Bot;
		}
	};
</script>

<Card class="p-6">
	<div class="flex items-center space-x-3 mb-6">
		<svelte:component this={currentConfig.icon} class="h-6 w-6 text-primary" />
		<div>
			<h3 class="text-lg font-semibold">{title}</h3>
			<p class="text-sm text-muted-foreground">{description}</p>
		</div>
	</div>

	<!-- 任務輸入 -->
	<div class="space-y-4 mb-6">
		<div>
			<label for="task-input" class="block text-sm font-medium mb-2">
				{currentConfig.inputLabel}
			</label>
			<textarea
				id="task-input"
				bind:value={taskInput}
				placeholder={currentConfig.placeholder}
				rows="4"
				class="input resize-vertical"
				disabled={isRunning}
			></textarea>
		</div>

		<!-- 任務選項 -->
		{#if currentConfig.options && currentConfig.options.length > 0}
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				{#each currentConfig.options as option}
					<div>
						<label for={option.key} class="block text-sm font-medium mb-1">
							{option.label}
						</label>

						{#if option.type === 'select'}
							<select
								id={option.key}
								bind:value={taskOptions[option.key]}
								class="select"
								disabled={isRunning}
							>
								{#each option.values as value}
									<option {value}>{value}</option>
								{/each}
							</select>
						{:else if option.type === 'number'}
							<input
								id={option.key}
								type="number"
								bind:value={taskOptions[option.key]}
								class="input"
								disabled={isRunning}
							/>
						{:else}
							<input
								id={option.key}
								type="text"
								bind:value={taskOptions[option.key]}
								placeholder={option.placeholder || ''}
								class="input"
								disabled={isRunning}
							/>
						{/if}
					</div>
				{/each}
			</div>
		{/if}
	</div>

	<!-- 執行按鈕 -->
	<div class="flex items-center space-x-3 mb-6">
		<Button
			variant="default"
			onclick={executeTask}
			disabled={isRunning || !taskInput.trim() || !simpleAgentManager.isReady()}
		>
			{#if isRunning}
				<Loader2 class="h-4 w-4 mr-2 animate-spin" />
				執行中...
			{:else}
				<Play class="h-4 w-4 mr-2" />
				執行任務
			{/if}
		</Button>

		{#if taskResult}
			<Button variant="outline" onclick={clearResult}>
				<Square class="h-4 w-4 mr-2" />
				清除結果
			</Button>
		{/if}

		<div class="text-sm text-muted-foreground">
			{#if !simpleAgentManager.isReady()}
				Agent 未就緒
			{:else if isRunning}
				正在處理...
			{:else}
				就緒
			{/if}
		</div>
	</div>

	<!-- 任務結果 -->
	{#if taskResult}
		<div class="border-t pt-6">
			<div class="flex items-center space-x-2 mb-4">
				<svelte:component
					this={getStatusIcon(taskResult.status)}
					class="h-5 w-5 {getStatusColor(taskResult.status)} {taskResult.status === 'running'
						? 'animate-spin'
						: ''}"
				/>
				<h4 class="font-medium">執行結果</h4>
				<span class="text-sm text-muted-foreground">
					({taskResult.executionTime}ms)
				</span>
			</div>

			<div class="bg-muted/30 rounded-lg p-4">
				<pre class="whitespace-pre-wrap text-sm">{formatResult(taskResult)}</pre>
			</div>

			{#if taskResult.output && typeof taskResult.output === 'object' && taskResult.output.usage}
				<div class="mt-3 text-xs text-muted-foreground">
					使用量：{JSON.stringify(taskResult.output.usage)}
				</div>
			{/if}
		</div>
	{/if}

	<!-- 歷史記錄 -->
	{#if taskHistory.length > 0}
		<div class="border-t pt-6 mt-6">
			<h4 class="font-medium mb-4">最近執行記錄</h4>
			<div class="space-y-2 max-h-60 overflow-y-auto">
				{#each taskHistory as historyItem}
					<div class="flex items-center justify-between p-3 bg-muted/20 rounded-lg text-sm">
						<div class="flex items-center space-x-2">
							<svelte:component
								this={getStatusIcon(historyItem.status)}
								class="h-4 w-4 {getStatusColor(historyItem.status)}"
							/>
							<span class="font-mono text-xs">{historyItem.taskId.slice(-8)}</span>
							<span class="text-muted-foreground">
								{historyItem.completedAt.toLocaleTimeString()}
							</span>
						</div>
						<span class="text-muted-foreground">
							{historyItem.executionTime}ms
						</span>
					</div>
				{/each}
			</div>
		</div>
	{/if}
</Card>
