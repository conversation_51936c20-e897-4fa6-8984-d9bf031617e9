<script lang="ts">
	import { onMount } from 'svelte';
	import Icon from '$lib/components/ui/Icon.svelte';
	import { <PERSON><PERSON>, Card } from '$components/ui';
	import { browser } from '$app/environment';
	import MCPServerConfig from './MCPServerConfig.svelte';
	import MCPServerStatus from './MCPServerStatus.svelte';

	// 狀態變數
	let serverInfo = $state<any>(null);
	let toolStats = $state<any>(null);
	let healthStatus = $state<any>(null);
	let isLoading = $state(false);
	let error = $state<string | null>(null);
	let testResults = $state<any>(null);
	let mcpServers = $state<Record<string, any>>({});
	let activeTab = $state<'status' | 'config'>('status');

	// 定時器
	let statusInterval: NodeJS.Timeout;

	onMount(() => {
		if (browser) {
			loadMCPServers();
			updateStatus();
			// 每 5 秒更新一次狀態
			statusInterval = setInterval(updateStatus, 5000);
		}
	});

	// 使用 $effect 來管理清理邏輯
	$effect(() => {
		return () => {
			if (statusInterval) {
				clearInterval(statusInterval);
			}
		};
	});

	// 載入 MCP 服務器配置
	function loadMCPServers() {
		if (!browser) return;

		try {
			const saved = localStorage.getItem('mcp-servers-config');
			if (saved) {
				mcpServers = JSON.parse(saved);
			}
		} catch (err) {
			console.error('Failed to load MCP servers config:', err);
		}
	}

	async function updateStatus() {
		if (!browser) return;

		try {
			const { getMCPServerInfo, getMCPToolStats, healthCheck } = await import('$lib/mcp/server');
			serverInfo = getMCPServerInfo();
			toolStats = getMCPToolStats();
			healthStatus = await healthCheck();
		} catch (err) {
			console.error('Failed to update MCP status:', err);
		}
	}

	async function handleStartServer() {
		if (!browser) return;

		isLoading = true;
		error = null;

		try {
			const { startMCPServer } = await import('$lib/mcp/server');
			await startMCPServer();
			await updateStatus();
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to start server';
		} finally {
			isLoading = false;
		}
	}

	async function handleStopServer() {
		if (!browser) return;

		isLoading = true;
		error = null;

		try {
			const { stopMCPServer } = await import('$lib/mcp/server');
			await stopMCPServer();
			await updateStatus();
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to stop server';
		} finally {
			isLoading = false;
		}
	}

	async function handleTestConnection() {
		if (!browser) return;

		isLoading = true;
		error = null;
		testResults = null;

		try {
			const { testMCPConnection } = await import('$lib/mcp/client');
			// 這裡需要實際的 MCP 服務器命令
			// 在實際部署中，這應該是指向編譯後的 MCP 服務器可執行文件
			const command = 'node';
			const args = ['dist/mcp-server.js']; // 假設的路徑

			testResults = await testMCPConnection(command, args);
		} catch (err) {
			error = err instanceof Error ? err.message : 'Connection test failed';
		} finally {
			isLoading = false;
		}
	}

	function getStatusColor(status: string): string {
		switch (status) {
			case 'healthy': return 'text-green-600';
			case 'unhealthy': return 'text-red-600';
			default: return 'text-yellow-600';
		}
	}

	function getStatusIcon(status: string) {
		switch (status) {
			case 'healthy': return CheckCircle;
			case 'unhealthy': return XCircle;
			default: return AlertCircle;
		}
	}
</script>

<div class="mcp-dashboard space-y-6">
	<!-- 標題 -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold flex items-center gap-2">
				<Server class="h-8 w-8 text-primary" />
				MCP Server 管理
			</h1>
			<p class="text-muted-foreground mt-1">
				Model Context Protocol 服務器狀態和管理
			</p>
		</div>

		<div class="flex items-center gap-2">
			<Button variant="outline" size="sm" on:click={updateStatus} disabled={isLoading}>
				<RefreshCw class="h-4 w-4 mr-1 {isLoading ? 'animate-spin' : ''}" />
				刷新狀態
			</Button>
		</div>
	</div>

	<!-- 標籤頁導航 -->
	<div class="border-b border-border">
		<nav class="flex space-x-6">
			<button
				class="py-2 px-1 border-b-2 font-medium text-sm {activeTab === 'status' ? 'border-primary text-primary' : 'border-transparent text-muted-foreground hover:text-foreground'}"
				on:click={() => activeTab = 'status'}
			>
				<Monitor class="h-4 w-4 inline mr-2" />
				服務器狀態
			</button>

			<button
				class="py-2 px-1 border-b-2 font-medium text-sm {activeTab === 'config' ? 'border-primary text-primary' : 'border-transparent text-muted-foreground hover:text-foreground'}"
				on:click={() => activeTab = 'config'}
			>
				<Cog class="h-4 w-4 inline mr-2" />
				服務器配置
			</button>
		</nav>
	</div>

	<!-- 錯誤提示 -->
	{#if error}
		<Card class="p-4 bg-destructive/10 border-destructive/20">
			<div class="flex items-center gap-2 text-destructive">
				<XCircle class="h-4 w-4" />
				<span class="font-medium">錯誤：</span>
				<span>{error}</span>
			</div>
		</Card>
	{/if}

	<!-- 標籤頁內容 -->
	{#if activeTab === 'status'}
		<!-- 服務器狀態標籤頁 -->
		<div class="space-y-6">
			<!-- MCP 服務器狀態列表 -->
			{#if Object.keys(mcpServers).length > 0}
				<div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
					{#each Object.entries(mcpServers) as [key, server]}
						<MCPServerStatus serverKey={key} serverConfig={server} />
					{/each}
				</div>
			{:else}
				<Card class="p-8 text-center">
					<Server class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
					<h3 class="text-lg font-medium mb-2">沒有配置的 MCP 服務器</h3>
					<p class="text-muted-foreground mb-4">請先在配置標籤頁中添加 MCP 服務器</p>
					<Button on:click={() => activeTab = 'config'}>
						<Cog class="h-4 w-4 mr-2" />
						前往配置
					</Button>
				</Card>
			{/if}

			<!-- Life Note MCP 服務器狀態 (內建) -->
			<div class="mt-6">
				<h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
					<Database class="h-5 w-5" />
					Life Note MCP 服務器
				</h3>

				<Card class="p-6">
					<div class="flex items-center justify-between mb-4">
						<h4 class="font-medium">內建 MCP 服務器</h4>

						{#if serverInfo}
							<div class="flex items-center gap-2">
								{#if serverInfo.isRunning}
									<div class="flex items-center gap-1 text-green-600">
										<CheckCircle class="h-4 w-4" />
										<span class="text-sm font-medium">運行中</span>
									</div>
								{:else}
									<div class="flex items-center gap-1 text-red-600">
										<XCircle class="h-4 w-4" />
										<span class="text-sm font-medium">已停止</span>
									</div>
								{/if}
							</div>
						{/if}
					</div>

			{#if serverInfo}
				<div class="space-y-3">
					<div class="grid grid-cols-2 gap-4 text-sm">
						<div>
							<span class="text-muted-foreground">名稱：</span>
							<span class="font-medium">{serverInfo.name}</span>
						</div>
						<div>
							<span class="text-muted-foreground">版本：</span>
							<span class="font-medium">{serverInfo.version}</span>
						</div>
						<div>
							<span class="text-muted-foreground">工具數量：</span>
							<span class="font-medium">{serverInfo.tools.total}</span>
						</div>
						<div>
							<span class="text-muted-foreground">資源數量：</span>
							<span class="font-medium">{serverInfo.resources}</span>
						</div>
					</div>

					<div class="pt-2">
						<p class="text-sm text-muted-foreground mb-2">描述：</p>
						<p class="text-sm">{serverInfo.description}</p>
					</div>
				</div>
			{:else}
				<div class="text-center py-4">
					<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
					<p class="text-sm text-muted-foreground">載入服務器信息...</p>
				</div>
			{/if}

			<!-- 控制按鈕 -->
			<div class="flex gap-2 mt-4 pt-4 border-t">
				{#if serverInfo?.isRunning}
					<Button variant="destructive" size="sm" onclick={handleStopServer} disabled={isLoading}>
						<Square class="h-4 w-4 mr-1" />
						停止服務器
					</Button>
				{:else}
					<Button size="sm" onclick={handleStartServer} disabled={isLoading}>
						<Play class="h-4 w-4 mr-1" />
						啟動服務器
					</Button>
				{/if}

				<Button variant="outline" size="sm" onclick={handleTestConnection} disabled={isLoading}>
					<Settings class="h-4 w-4 mr-1" />
					測試連接
				</Button>
			</div>
		</Card>

		<!-- 健康狀態 -->
		<Card class="p-6">
			<h3 class="text-lg font-semibold flex items-center gap-2 mb-4">
				<Activity class="h-5 w-5" />
				健康檢查
			</h3>

			{#if healthStatus}
				<div class="space-y-3">
					<div class="flex items-center justify-between">
						<span class="text-sm text-muted-foreground">狀態：</span>
						<div class="flex items-center gap-1 {getStatusColor(healthStatus.status)}">
							<svelte:component this={getStatusIcon(healthStatus.status)} class="h-4 w-4" />
							<span class="font-medium capitalize">{healthStatus.status}</span>
						</div>
					</div>

					<div class="flex items-center justify-between">
						<span class="text-sm text-muted-foreground">檢查時間：</span>
						<span class="text-sm font-medium">
							{new Date(healthStatus.timestamp).toLocaleString()}
						</span>
					</div>

					{#if healthStatus.errors && healthStatus.errors.length > 0}
						<div class="mt-3">
							<p class="text-sm text-muted-foreground mb-2">錯誤：</p>
							<div class="space-y-1">
								{#each healthStatus.errors as error}
									<div class="text-sm text-destructive bg-destructive/10 p-2 rounded">
										{error}
									</div>
								{/each}
							</div>
						</div>
					{/if}
				</div>
			{:else}
				<div class="text-center py-4">
					<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
					<p class="text-sm text-muted-foreground">執行健康檢查...</p>
				</div>
			{/if}
		</Card>

		<!-- 工具統計 -->
		<Card class="p-6">
			<h3 class="text-lg font-semibold flex items-center gap-2 mb-4">
				<Wrench class="h-5 w-5" />
				可用工具
			</h3>

			{#if toolStats}
				<div class="space-y-3">
					<div class="text-sm">
						<span class="text-muted-foreground">總計：</span>
						<span class="font-medium">{toolStats.availableTools.length} 個工具</span>
					</div>

					<div class="space-y-2">
						{#each toolStats.availableTools as tool}
							<div class="flex items-center justify-between p-2 bg-muted/50 rounded">
								<span class="text-sm font-medium">{tool}</span>
								{#if toolStats.toolHelp[tool]}
									<span class="text-xs text-muted-foreground max-w-xs truncate">
										{toolStats.toolHelp[tool]}
									</span>
								{/if}
							</div>
						{/each}
					</div>
				</div>
			{:else}
				<div class="text-center py-4">
					<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
					<p class="text-sm text-muted-foreground">載入工具信息...</p>
				</div>
			{/if}
		</Card>

		<!-- 測試結果 -->
		{#if testResults}
			<Card class="p-6">
				<h3 class="text-lg font-semibold flex items-center gap-2 mb-4">
					<Database class="h-5 w-5" />
					連接測試結果
				</h3>

				<div class="space-y-3">
					<div class="flex items-center justify-between">
						<span class="text-sm text-muted-foreground">測試狀態：</span>
						<div class="flex items-center gap-1 {testResults.success ? 'text-green-600' : 'text-red-600'}">
							<svelte:component this={testResults.success ? CheckCircle : XCircle} class="h-4 w-4" />
							<span class="font-medium">{testResults.success ? '成功' : '失敗'}</span>
						</div>
					</div>

					{#if testResults.results.length > 0}
						<div>
							<p class="text-sm text-muted-foreground mb-2">測試步驟：</p>
							<div class="space-y-1">
								{#each testResults.results as result}
									<div class="flex items-center justify-between text-sm p-2 bg-muted/50 rounded">
										<span>{result.step}</span>
										<div class="flex items-center gap-1 {result.success ? 'text-green-600' : 'text-red-600'}">
											<svelte:component this={result.success ? CheckCircle : XCircle} class="h-3 w-3" />
											<span class="text-xs">{result.success ? '成功' : '失敗'}</span>
										</div>
									</div>
								{/each}
							</div>
						</div>
					{/if}

					{#if testResults.errors.length > 0}
						<div>
							<p class="text-sm text-muted-foreground mb-2">錯誤：</p>
							<div class="space-y-1">
								{#each testResults.errors as error}
									<div class="text-sm text-destructive bg-destructive/10 p-2 rounded">
										{error}
									</div>
								{/each}
							</div>
						</div>
					{/if}
				</div>
			</Card>
		{/if}
	</div>
</div>

<style>
	.mcp-dashboard {
		max-width: 1200px;
		margin: 0 auto;
	}
</style>
