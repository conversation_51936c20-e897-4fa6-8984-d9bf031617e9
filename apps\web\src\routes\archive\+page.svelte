<script>
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import Icon from '$lib/components/ui/Icon.svelte';
  import Button from '$components/ui/Button.svelte';
  import Card from '$components/ui/Card.svelte';
  import { allNotes } from '$stores/notes';

  // 歸檔筆記數據
  let archivedNotes = $state([]);
  let searchQuery = $state('');
  let selectedTag = $state('');
  let sortBy = $state('archivedAt'); // archivedAt, title, updatedAt
  let sortOrder = $state('desc'); // asc, desc
  let viewMode = $state('grid'); // grid, list
  let showFilters = $state(false);

  // 過濾和排序
  const filteredNotes = $derived(archivedNotes
    .filter(note => {
      const matchesSearch = note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           note.content.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesTag = !selectedTag || note.tags.some(tag => tag.name === selectedTag);
      return matchesSearch && matchesTag;
    })
    .sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'updatedAt':
          aValue = new Date(a.updatedAt);
          bValue = new Date(b.updatedAt);
          break;
        case 'archivedAt':
        default:
          aValue = new Date(a.archivedAt || a.updatedAt);
          bValue = new Date(b.archivedAt || b.updatedAt);
          break;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    }));

  // 獲取所有標籤
  const allTags = $derived([...new Set(archivedNotes.flatMap(note => note.tags.map(tag => tag.name)))]);

  onMount(() => {
    loadArchivedNotes();
  });

  const loadArchivedNotes = () => {
    // 模擬歸檔筆記數據
    archivedNotes = [
      {
        id: 'archived-1',
        title: '舊項目文檔',
        content: '這是一個已完成項目的文檔記錄...',
        tags: [
          { id: 'tag-1', name: '項目', color: '#3b82f6' },
          { id: 'tag-2', name: '文檔', color: '#10b981' }
        ],
        status: 'archived',
        priority: 'medium',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-02-20T15:30:00Z',
        archivedAt: '2024-03-01T09:00:00Z'
      },
      {
        id: 'archived-2',
        title: '學習筆記 - React Hooks',
        content: '# React Hooks 學習筆記\n\n## useState\n...',
        tags: [
          { id: 'tag-3', name: '學習', color: '#8b5cf6' },
          { id: 'tag-4', name: 'React', color: '#f59e0b' }
        ],
        status: 'archived',
        priority: 'low',
        createdAt: '2024-01-10T14:00:00Z',
        updatedAt: '2024-01-25T16:45:00Z',
        archivedAt: '2024-02-15T11:20:00Z'
      },
      {
        id: 'archived-3',
        title: '會議記錄 - Q1 回顧',
        content: '## Q1 季度回顧會議\n\n### 參與者\n- 張三\n- 李四\n...',
        tags: [
          { id: 'tag-5', name: '會議', color: '#ef4444' },
          { id: 'tag-6', name: 'Q1', color: '#06b6d4' }
        ],
        status: 'archived',
        priority: 'high',
        createdAt: '2024-03-28T09:00:00Z',
        updatedAt: '2024-03-28T11:30:00Z',
        archivedAt: '2024-04-01T17:00:00Z'
      }
    ];
  };

  const restoreNote = (note) => {
    if (confirm(`確定要恢復筆記「${note.title}」嗎？`)) {
      // 這裡應該調用 API 恢復筆記
      archivedNotes = archivedNotes.filter(n => n.id !== note.id);
      showNotification('筆記已恢復', 'success');
    }
  };

  const permanentlyDeleteNote = (note) => {
    if (confirm(`確定要永久刪除筆記「${note.title}」嗎？此操作無法撤銷。`)) {
      archivedNotes = archivedNotes.filter(n => n.id !== note.id);
      showNotification('筆記已永久刪除', 'success');
    }
  };

  const viewNote = (note) => {
    goto(`/notes/${note.id}`);
  };

  const toggleSort = (field) => {
    if (sortBy === field) {
      sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      sortBy = field;
      sortOrder = 'desc';
    }
  };

  const showNotification = (message, type) => {
    console.log(`${type}: ${message}`);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('zh-TW', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getPreview = (content) => {
    return content.replace(/[#*`]/g, '').substring(0, 100) + '...';
  };
</script>

<svelte:head>
  <title>歸檔 - Life Note</title>
</svelte:head>

<div class="archive-page h-full flex flex-col">
  <!-- Header -->
  <header class="flex-shrink-0 border-b border-border bg-background/95 backdrop-blur">
    <div class="container mx-auto px-4 py-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold flex items-center">
            <Icon icon="archive" size={24} class="mr-2" />
            歸檔
          </h1>
          <p class="text-muted-foreground mt-1">
            查看和管理已歸檔的筆記
          </p>
        </div>
        
        <div class="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onclick={() => showFilters = !showFilters}
          >
            <Icon icon="filter" size={16} class="mr-2" />
            篩選
          </Button>

          <Button
            variant="outline"
            size="sm"
            onclick={() => viewMode = viewMode === 'grid' ? 'list' : 'grid'}
          >
            {#if viewMode === 'grid'}
              <Icon icon="list" size={16} />
            {:else}
              <Icon icon="grid-3x3" size={16} />
            {/if}
          </Button>
        </div>
      </div>
      
      <!-- Search and Filters -->
      <div class="mt-6 space-y-4">
        <div class="relative max-w-md">
          <Icon icon="search" size={16} class="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
          <input
            type="text"
            placeholder="搜尋歸檔筆記..."
            bind:value={searchQuery}
            class="w-full pl-10 pr-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
          />
        </div>
        
        {#if showFilters}
          <div class="flex flex-wrap gap-4 p-4 bg-muted/30 rounded-lg">
            <div>
              <label for="tag-filter" class="block text-sm font-medium mb-1">標籤</label>
              <select
                id="tag-filter"
                bind:value={selectedTag}
                class="px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="">所有標籤</option>
                {#each allTags as tag}
                  <option value={tag}>{tag}</option>
                {/each}
              </select>
            </div>
            
            <div>
              <label for="sort-by" class="block text-sm font-medium mb-1">排序</label>
              <div class="flex space-x-2">
                <select
                  id="sort-by"
                  bind:value={sortBy}
                  class="px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                >
                  <option value="archivedAt">歸檔時間</option>
                  <option value="title">標題</option>
                  <option value="updatedAt">更新時間</option>
                </select>
                
                <Button
                  variant="outline"
                  size="sm"
                  onclick={() => sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'}
                >
                  {#if sortOrder === 'asc'}
                    <Icon icon="arrow-up-a-z" size={16} />
                  {:else}
                    <Icon icon="arrow-down-z-a" size={16} />
                  {/if}
                </Button>
              </div>
            </div>
          </div>
        {/if}
      </div>
    </div>
  </header>

  <!-- Content -->
  <main class="flex-1 overflow-auto">
    <div class="container mx-auto px-4 py-6">
      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Card class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <Icon icon="archive" size={20} class="text-blue-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-muted-foreground">總歸檔數</p>
              <p class="text-2xl font-bold">{archivedNotes.length}</p>
            </div>
          </div>
        </Card>

        <Card class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <Icon icon="calendar" size={20} class="text-green-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-muted-foreground">本月歸檔</p>
              <p class="text-2xl font-bold">
                {archivedNotes.filter(note => {
                  const archivedDate = new Date(note.archivedAt || note.updatedAt);
                  const now = new Date();
                  return archivedDate.getMonth() === now.getMonth() && 
                         archivedDate.getFullYear() === now.getFullYear();
                }).length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <Tag class="h-5 w-5 text-purple-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-muted-foreground">標籤數量</p>
              <p class="text-2xl font-bold">{allTags.length}</p>
            </div>
          </div>
        </Card>
      </div>

      <!-- Notes -->
      {#if filteredNotes.length > 0}
        <div class="grid {viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'} gap-4">
          {#each filteredNotes as note (note.id)}
            <Card class="p-4 hover:shadow-md transition-shadow">
              <div class="flex items-start justify-between mb-3">
                <h3 class="font-semibold text-lg line-clamp-2">{note.title}</h3>
                <div class="flex items-center space-x-1 ml-2">
                  <Button variant="ghost" size="sm" onclick={() => viewNote(note)}>
                    <Icon icon="eye" size={12} />
                  </Button>
                  <Button variant="ghost" size="sm" onclick={() => restoreNote(note)}>
                    <Icon icon="rotate-ccw" size={12} />
                  </Button>
                  <Button variant="ghost" size="sm" onclick={() => permanentlyDeleteNote(note)}>
                    <Icon icon="trash-2" size={12} />
                  </Button>
                </div>
              </div>
              
              {#if viewMode === 'grid'}
                <p class="text-muted-foreground text-sm mb-3 line-clamp-3">
                  {getPreview(note.content)}
                </p>
              {/if}
              
              <div class="flex flex-wrap gap-1 mb-3">
                {#each note.tags as tag}
                  <span 
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border"
                    style="background-color: {tag.color}20; border-color: {tag.color}; color: {tag.color}"
                  >
                    {tag.name}
                  </span>
                {/each}
              </div>
              
              <div class="flex items-center justify-between text-xs text-muted-foreground">
                <span>歸檔於 {formatDate(note.archivedAt || note.updatedAt)}</span>
                <span class="capitalize px-2 py-1 rounded-full bg-muted">
                  {note.priority}
                </span>
              </div>
            </Card>
          {/each}
        </div>
      {:else}
        <div class="text-center py-12">
          <Icon icon="archive" size={48} class="text-muted-foreground mx-auto mb-4" />
          <h3 class="text-lg font-medium text-muted-foreground mb-2">
            {searchQuery || selectedTag ? '沒有找到匹配的歸檔筆記' : '還沒有歸檔的筆記'}
          </h3>
          <p class="text-muted-foreground">
            {searchQuery || selectedTag ? '嘗試調整搜尋條件或篩選器' : '歸檔的筆記會出現在這裡'}
          </p>
        </div>
      {/if}
    </div>
  </main>
</div>
