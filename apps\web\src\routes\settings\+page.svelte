<script>
  import { onMount } from 'svelte';
  import Icon from '$lib/components/ui/Icon.svelte';
  import {
    User,
    Palette,
    Settings,
    Bell,
    Database,
    Shield
  } from 'svelte-lucide';

  import Button from '$components/ui/Button.svelte';
  import Card from '$components/ui/Card.svelte';
  import DependencyMonitoringSettings from '$components/settings/DependencyMonitoringSettings.svelte';

  // 設置數據
  let settings = $state({
    // 個人設置
    profile: {
      name: '',
      email: '',
      avatar: ''
    },
    
    // 外觀設置
    appearance: {
      theme: 'system', // light, dark, system
      fontSize: 'medium', // small, medium, large
      fontFamily: 'system', // system, serif, mono
      compactMode: false
    },
    
    // 編輯器設置
    editor: {
      defaultView: 'split', // edit, preview, split
      autoSave: true,
      autoSaveInterval: 30, // seconds
      spellCheck: true,
      lineNumbers: false,
      wordWrap: true,
      tabSize: 2
    },
    
    // 通知設置
    notifications: {
      enabled: true,
      sound: true,
      desktop: false,
      email: false
    },
    
    // 隱私設置
    privacy: {
      analytics: true,
      crashReports: true,
      shareUsageData: false
    },
    
    // 數據設置
    data: {
      autoBackup: true,
      backupInterval: 'daily', // daily, weekly, monthly
      maxBackups: 10,
      syncEnabled: false
    }
  });

  let activeTab = $state('profile');
  let hasChanges = $state(false);
  let isSaving = $state(false);

  // 監聽設置變化
  $effect(() => {
    // 這裡可以添加設置變化的邏輯
    hasChanges = true;
  });

  onMount(() => {
    loadSettings();
  });

  const loadSettings = () => {
    // 從 localStorage 載入設置
    const savedSettings = localStorage.getItem('life-note-settings');
    if (savedSettings) {
      try {
        settings = { ...settings, ...JSON.parse(savedSettings) };
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    }
    hasChanges = false;
  };

  const saveSettings = async () => {
    isSaving = true;
    try {
      // 保存到 localStorage
      localStorage.setItem('life-note-settings', JSON.stringify(settings));
      
      // 模擬 API 調用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      hasChanges = false;
      showNotification('設置已保存', 'success');
    } catch (error) {
      console.error('Failed to save settings:', error);
      showNotification('保存設置失敗', 'error');
    } finally {
      isSaving = false;
    }
  };

  const resetSettings = () => {
    if (confirm('確定要重置所有設置嗎？此操作無法撤銷。')) {
      localStorage.removeItem('life-note-settings');
      location.reload();
    }
  };

  const exportData = () => {
    // 導出數據邏輯
    const data = {
      settings,
      exportDate: new Date().toISOString(),
      version: '1.0.0'
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `life-note-settings-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const importData = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target.result);
          if (data.settings) {
            settings = { ...settings, ...data.settings };
            hasChanges = true;
            showNotification('設置已導入', 'success');
          }
        } catch (error) {
          showNotification('導入失敗：文件格式錯誤', 'error');
        }
      };
      reader.readAsText(file);
    }
  };

  const clearAllData = () => {
    if (confirm('確定要清除所有數據嗎？此操作無法撤銷，將刪除所有筆記、設置和用戶數據。')) {
      if (confirm('最後確認：這將永久刪除所有數據，您確定要繼續嗎？')) {
        localStorage.clear();
        location.reload();
      }
    }
  };

  const showNotification = (message, type) => {
    // 簡單的通知實現
    console.log(`${type}: ${message}`);
  };

  const tabs = [
    { id: 'profile', label: '個人資料', icon: User },
    { id: 'appearance', label: '外觀', icon: Palette },
    { id: 'editor', label: '編輯器', icon: Settings },
    { id: 'notifications', label: '通知', icon: Bell },
    { id: 'monitoring', label: '依賴監控', icon: Database },
    { id: 'privacy', label: '隱私', icon: Shield },
    { id: 'data', label: '數據', icon: Database }
  ];
</script>

<svelte:head>
  <title>設置 - Life Note</title>
</svelte:head>

<div class="settings-page h-full flex flex-col">
  <!-- Header -->
  <header class="flex-shrink-0 border-b border-border bg-background/95 backdrop-blur">
    <div class="container mx-auto px-4 py-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold flex items-center">
            <Icon icon="settings" class="h-6 w-6 mr-2" />
            設置
          </h1>
          <p class="text-muted-foreground mt-1">
            自定義您的 Life Note 體驗
          </p>
        </div>

        <div class="flex items-center space-x-2">
          {#if hasChanges}
            <Button variant="outline" onclick={loadSettings}>
              <Icon icon="rotate-ccw" class="h-4 w-4 mr-2" />
              重置
            </Button>
            <Button onclick={saveSettings} disabled={isSaving}>
              <Icon icon="save" class="h-4 w-4 mr-2" />
              {isSaving ? '保存中...' : '保存設置'}
            </Button>
          {/if}
        </div>
      </div>
    </div>
  </header>

  <!-- Content -->
  <main class="flex-1 overflow-hidden flex">
    <!-- Sidebar -->
    <aside class="w-64 border-r border-border bg-muted/30 p-4">
      <nav class="space-y-1">
        {#each tabs as tab}
          <button
            class="w-full flex items-center px-3 py-2 text-left rounded-md transition-colors {
              activeTab === tab.id
                ? 'bg-primary text-primary-foreground'
                : 'hover:bg-muted text-muted-foreground hover:text-foreground'
            }"
            onclick={() => activeTab = tab.id}
          >
            <Icon icon={tab.icon} class="h-4 w-4 mr-3" />
            {tab.label}
          </button>
        {/each}
      </nav>
    </aside>

    <!-- Settings Content -->
    <div class="flex-1 overflow-auto p-6">
      {#if activeTab === 'profile'}
        <Card class="p-6">
          <h3 class="text-lg font-semibold mb-4">個人資料</h3>
          <div class="space-y-4">
            <div>
              <label for="profile-name" class="block text-sm font-medium mb-1">姓名</label>
              <input
                id="profile-name"
                type="text"
                bind:value={settings.profile.name}
                placeholder="輸入您的姓名"
                class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>
            <div>
              <label for="profile-email" class="block text-sm font-medium mb-1">電子郵件</label>
              <input
                id="profile-email"
                type="email"
                bind:value={settings.profile.email}
                placeholder="輸入您的電子郵件"
                class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>
          </div>
        </Card>
      
      {:else if activeTab === 'appearance'}
        <Card class="p-6">
          <h3 class="text-lg font-semibold mb-4">外觀設置</h3>
          <div class="space-y-6">
            <div>
              <label for="theme-select" class="block text-sm font-medium mb-2">主題</label>
              <select
                id="theme-select"
                bind:value={settings.appearance.theme}
                class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="light">淺色</option>
                <option value="dark">深色</option>
                <option value="system">跟隨系統</option>
              </select>
            </div>
            
            <div>
              <label for="font-size-select" class="block text-sm font-medium mb-2">字體大小</label>
              <select
                id="font-size-select"
                bind:value={settings.appearance.fontSize}
                class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="small">小</option>
                <option value="medium">中</option>
                <option value="large">大</option>
              </select>
            </div>
            
            <div>
              <label for="font-family-select" class="block text-sm font-medium mb-2">字體系列</label>
              <select
                id="font-family-select"
                bind:value={settings.appearance.fontFamily}
                class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="system">系統字體</option>
                <option value="serif">襯線字體</option>
                <option value="mono">等寬字體</option>
              </select>
            </div>
            
            <div class="flex items-center">
              <input
                type="checkbox"
                id="compactMode"
                bind:checked={settings.appearance.compactMode}
                class="mr-2"
              />
              <label for="compactMode" class="text-sm font-medium">緊湊模式</label>
            </div>
          </div>
        </Card>
      
      {:else if activeTab === 'editor'}
        <Card class="p-6">
          <h3 class="text-lg font-semibold mb-4">編輯器設置</h3>
          <div class="space-y-6">
            <div>
              <label for="default-view-select" class="block text-sm font-medium mb-2">默認視圖</label>
              <select
                id="default-view-select"
                bind:value={settings.editor.defaultView}
                class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="edit">編輯</option>
                <option value="preview">預覽</option>
                <option value="split">分割視圖</option>
              </select>
            </div>
            
            <div class="flex items-center">
              <input
                type="checkbox"
                id="autoSave"
                bind:checked={settings.editor.autoSave}
                class="mr-2"
              />
              <label for="autoSave" class="text-sm font-medium">自動保存</label>
            </div>
            
            {#if settings.editor.autoSave}
              <div>
                <label for="auto-save-interval" class="block text-sm font-medium mb-2">自動保存間隔（秒）</label>
                <input
                  id="auto-save-interval"
                  type="number"
                  bind:value={settings.editor.autoSaveInterval}
                  min="10"
                  max="300"
                  class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                />
              </div>
            {/if}
            
            <div class="flex items-center">
              <input
                type="checkbox"
                id="spellCheck"
                bind:checked={settings.editor.spellCheck}
                class="mr-2"
              />
              <label for="spellCheck" class="text-sm font-medium">拼寫檢查</label>
            </div>
            
            <div class="flex items-center">
              <input
                type="checkbox"
                id="lineNumbers"
                bind:checked={settings.editor.lineNumbers}
                class="mr-2"
              />
              <label for="lineNumbers" class="text-sm font-medium">顯示行號</label>
            </div>
            
            <div class="flex items-center">
              <input
                type="checkbox"
                id="wordWrap"
                bind:checked={settings.editor.wordWrap}
                class="mr-2"
              />
              <label for="wordWrap" class="text-sm font-medium">自動換行</label>
            </div>
          </div>
        </Card>
      
      {:else if activeTab === 'notifications'}
        <Card class="p-6">
          <h3 class="text-lg font-semibold mb-4">通知設置</h3>
          <div class="space-y-4">
            <div class="flex items-center">
              <input
                type="checkbox"
                id="notificationsEnabled"
                bind:checked={settings.notifications.enabled}
                class="mr-2"
              />
              <label for="notificationsEnabled" class="text-sm font-medium">啟用通知</label>
            </div>
            
            {#if settings.notifications.enabled}
              <div class="ml-6 space-y-4">
                <div class="flex items-center">
                  <input
                    type="checkbox"
                    id="notificationSound"
                    bind:checked={settings.notifications.sound}
                    class="mr-2"
                  />
                  <label for="notificationSound" class="text-sm font-medium">聲音提醒</label>
                </div>
                
                <div class="flex items-center">
                  <input
                    type="checkbox"
                    id="desktopNotifications"
                    bind:checked={settings.notifications.desktop}
                    class="mr-2"
                  />
                  <label for="desktopNotifications" class="text-sm font-medium">桌面通知</label>
                </div>
                
                <div class="flex items-center">
                  <input
                    type="checkbox"
                    id="emailNotifications"
                    bind:checked={settings.notifications.email}
                    class="mr-2"
                  />
                  <label for="emailNotifications" class="text-sm font-medium">電子郵件通知</label>
                </div>
              </div>
            {/if}
          </div>
        </Card>
      
      {:else if activeTab === 'privacy'}
        <Card class="p-6">
          <h3 class="text-lg font-semibold mb-4">隱私設置</h3>
          <div class="space-y-4">
            <div class="flex items-center">
              <input
                type="checkbox"
                id="analytics"
                bind:checked={settings.privacy.analytics}
                class="mr-2"
              />
              <label for="analytics" class="text-sm font-medium">允許分析數據收集</label>
            </div>
            
            <div class="flex items-center">
              <input
                type="checkbox"
                id="crashReports"
                bind:checked={settings.privacy.crashReports}
                class="mr-2"
              />
              <label for="crashReports" class="text-sm font-medium">發送崩潰報告</label>
            </div>
            
            <div class="flex items-center">
              <input
                type="checkbox"
                id="shareUsageData"
                bind:checked={settings.privacy.shareUsageData}
                class="mr-2"
              />
              <label for="shareUsageData" class="text-sm font-medium">分享使用數據</label>
            </div>
          </div>
        </Card>
      
      {:else if activeTab === 'monitoring'}
        <DependencyMonitoringSettings />

      {:else if activeTab === 'data'}
        <Card class="p-6">
          <h3 class="text-lg font-semibold mb-4">數據管理</h3>
          <div class="space-y-6">
            <div class="flex items-center">
              <input
                type="checkbox"
                id="autoBackup"
                bind:checked={settings.data.autoBackup}
                class="mr-2"
              />
              <label for="autoBackup" class="text-sm font-medium">自動備份</label>
            </div>
            
            <div class="space-y-4">
              <h4 class="font-medium">數據操作</h4>
              <div class="flex flex-wrap gap-2">
                <Button variant="outline" onclick={exportData}>
                  <Icon icon="download" class="h-4 w-4 mr-2" />
                  導出數據
                </Button>

                <label class="inline-flex">
                  <Button variant="outline" as="span">
                    <Icon icon="upload" class="h-4 w-4 mr-2" />
                    導入數據
                  </Button>
                  <input
                    type="file"
                    accept=".json"
                    onchange={importData}
                    class="hidden"
                  />
                </label>
              </div>
            </div>
            
            <div class="border-t pt-6">
              <h4 class="font-medium text-destructive mb-2">危險操作</h4>
              <p class="text-sm text-muted-foreground mb-4">
                以下操作無法撤銷，請謹慎操作。
              </p>
              <div class="space-y-2">
                <Button variant="outline" onclick={resetSettings}>
                  <Icon icon="rotate-ccw" class="h-4 w-4 mr-2" />
                  重置所有設置
                </Button>

                <Button variant="destructive" onclick={clearAllData}>
                  <Icon icon="trash-2" class="h-4 w-4 mr-2" />
                  清除所有數據
                </Button>
              </div>
            </div>
          </div>
        </Card>
      {/if}
    </div>
  </main>
</div>
